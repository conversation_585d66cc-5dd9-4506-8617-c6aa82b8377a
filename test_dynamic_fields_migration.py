#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态字段和字符串JSON metadata的迁移功能
"""

import os
import asyncio
import json
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# 加载环境变量
load_dotenv()

async def test_dynamic_fields_migration():
    """测试动态字段和字符串JSON metadata的迁移"""
    print("🧪 测试动态字段和字符串JSON metadata迁移")
    print("=" * 60)
    
    # 连接数据库
    uri = os.getenv("VECTOR_DB_URI", "")
    token = os.getenv("VECTOR_DB_TOKEN", "")
    
    db = MilvusVectorDB(uri=uri, token=token)
    await db.connect()
    print("✅ 数据库连接成功")
    
    # 测试集合名称
    test_collection = "test_dynamic_fields_source"
    target_collection = "test_dynamic_fields_target"
    
    try:
        # 1. 创建源集合并插入测试数据
        print(f"\n📝 创建源集合: {test_collection}")
        await db.create_collection(test_collection, 4)
        
        # 准备测试数据 - 模拟您的实际情况
        test_records = [
            {
                "vector": [1.0, 2.0, 3.0, 4.0],
                "content": "测试内容1",
                "metadata": '{"category": "AI", "type": "question", "priority": 1}',  # 字符串JSON
                # 动态字段
                "custom_field1": "自定义值1",
                "custom_field2": 123,
                "custom_field3": True,
                "timestamp": "2024-01-01T10:00:00Z",
                "source_system": "test_system"
            },
            {
                "vector": [5.0, 6.0, 7.0, 8.0],
                "content": "测试内容2",
                "metadata": '{"category": "ML", "type": "answer", "priority": 2}',  # 字符串JSON
                # 动态字段
                "custom_field1": "自定义值2",
                "custom_field2": 456,
                "custom_field3": False,
                "timestamp": "2024-01-01T11:00:00Z",
                "source_system": "test_system"
            },
            {
                "vector": [9.0, 10.0, 11.0, 12.0],
                "content": "测试内容3",
                "metadata": '{"category": "Data", "type": "document"}',  # 字符串JSON，缺少某些字段
                # 动态字段（不同的字段组合）
                "custom_field1": "自定义值3",
                "custom_field4": "新字段",
                "timestamp": "2024-01-01T12:00:00Z",
                "department": "research"
            }
        ]
        
        # 插入测试数据
        result = await db.insert_vectors(test_collection, test_records)
        print(f"✅ 插入测试数据: {result} 条记录")
        
        # 2. 查询源数据验证
        print(f"\n🔍 查询源集合数据")
        query_results = db.client.query(
            collection_name=test_collection,
            filter="",
            output_fields=["*"],
            limit=10
        )
        
        print(f"📊 源集合记录数: {len(query_results)}")
        
        # 分析源数据结构
        if query_results:
            sample_record = query_results[0]
            print(f"📋 源数据字段: {list(sample_record.keys())}")
            print(f"📋 metadata类型: {type(sample_record.get('metadata'))}")
            print(f"📋 metadata内容: {sample_record.get('metadata')}")
            
            # 检查动态字段
            standard_fields = {'id', 'vector', 'content', 'collection_name', 'metadata'}
            dynamic_fields = set(sample_record.keys()) - standard_fields
            print(f"📋 动态字段: {list(dynamic_fields)}")
        
        # 3. 创建目标集合
        print(f"\n🎯 创建目标集合: {target_collection}")
        await db.create_collection(target_collection, 4)
        
        # 4. 模拟迁移过程
        print(f"\n🚀 开始迁移数据")
        
        # 转换数据格式（模拟迁移脚本的处理）
        migrated_records = []
        dynamic_fields_found = set()
        
        for item in query_results:
            # 创建新的记录副本
            record = {}
            
            # 处理标准字段
            for key, value in item.items():
                if key == 'id':
                    # 跳过自动生成的id字段
                    continue
                elif key == 'metadata':
                    # 确保metadata是字典格式
                    if isinstance(value, str):
                        try:
                            record['metadata'] = json.loads(value)
                            print(f"[兼容性] 成功解析字符串格式的metadata")
                        except Exception as e:
                            print(f"[兼容性警告] 无法解析metadata字符串: {e}")
                            record['metadata'] = {}
                    else:
                        record['metadata'] = value if isinstance(value, dict) else {}
                else:
                    # 保留所有其他字段（包括动态字段）
                    record[key] = value
                    
                    # 记录动态字段
                    if key not in ['vector', 'content', 'collection_name', 'metadata']:
                        dynamic_fields_found.add(key)
            
            migrated_records.append(record)
        
        # 输出动态字段信息
        if dynamic_fields_found:
            print(f"[动态字段] 发现动态字段: {list(dynamic_fields_found)}")
        
        # 5. 插入到目标集合
        result = await db.insert_vectors(target_collection, migrated_records)
        print(f"✅ 迁移完成: {result} 条记录")
        
        # 6. 验证目标数据
        print(f"\n✅ 验证目标集合数据")
        target_results = db.client.query(
            collection_name=target_collection,
            filter="",
            output_fields=["*"],
            limit=10
        )
        
        print(f"📊 目标集合记录数: {len(target_results)}")
        
        if target_results:
            sample_target = target_results[0]
            print(f"📋 目标数据字段: {list(sample_target.keys())}")
            print(f"📋 metadata类型: {type(sample_target.get('metadata'))}")
            print(f"📋 metadata内容: {sample_target.get('metadata')}")
            
            # 检查动态字段是否保留
            target_dynamic_fields = set(sample_target.keys()) - standard_fields
            print(f"📋 保留的动态字段: {list(target_dynamic_fields)}")
        
        # 7. 对比验证
        print(f"\n🔍 迁移验证")
        print(f"源集合记录数: {len(query_results)}")
        print(f"目标集合记录数: {len(target_results)}")
        
        if len(query_results) == len(target_results):
            print("✅ 记录数量匹配")
        else:
            print("❌ 记录数量不匹配")
        
        # 验证字段完整性
        if query_results and target_results:
            source_fields = set(query_results[0].keys()) - {'id'}
            target_fields = set(target_results[0].keys()) - {'id'}
            
            missing_fields = source_fields - target_fields
            extra_fields = target_fields - source_fields
            
            if not missing_fields and not extra_fields:
                print("✅ 字段完整性验证通过")
            else:
                if missing_fields:
                    print(f"⚠️ 缺失字段: {list(missing_fields)}")
                if extra_fields:
                    print(f"ℹ️ 额外字段: {list(extra_fields)}")
        
        # 8. 测试搜索功能
        print(f"\n🔍 测试搜索功能")
        search_results = await db.search_vectors(
            collection=target_collection,
            vector=[1.0, 2.0, 3.0, 4.0],
            top_k=2
        )
        
        print(f"📊 搜索结果数: {len(search_results)}")
        if search_results:
            result = search_results[0]
            print(f"📋 搜索结果字段: {list(result.keys())}")
            print(f"📋 metadata: {result.get('metadata')}")
            
            # 检查动态字段是否在搜索结果中
            result_dynamic_fields = set(result.keys()) - {'content', 'collection', 'metadata', 'distance'}
            print(f"📋 搜索结果中的动态字段: {list(result_dynamic_fields)}")
        
        print(f"\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        try:
            print(f"\n🧹 清理测试数据")
            db.client.drop_collection(test_collection)
            db.client.drop_collection(target_collection)
            print("✅ 测试数据清理完成")
        except Exception as e:
            print(f"⚠️ 清理测试数据时出错: {e}")

async def main():
    """主函数"""
    await test_dynamic_fields_migration()

if __name__ == "__main__":
    asyncio.run(main())
