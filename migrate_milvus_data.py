#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus向量数据库迁移工具
支持在不重新向量化的情况下，将数据从源Milvus迁移到目标Milvus
"""

import os
import asyncio
import json
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# 加载环境变量
load_dotenv()

class MilvusDataMigrator:
    def __init__(self):
        # 源数据库配置（从环境变量读取）
        self.source_uri = os.getenv("VECTOR_DB_URI", "")
        self.source_token = os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = os.getenv("VECTOR_DB_DATABASE", "")
        
        # 目标数据库配置（待填写）
        self.target_uri = ""  # 请填写目标Milvus的URI
        self.target_token = ""  # 请填写目标Milvus的Token
        self.target_database = ""  # 请填写目标数据库名称
        
        self.source_db = None
        self.target_db = None
        
    async def connect_databases(self):
        """连接源数据库和目标数据库"""
        print("=== 连接数据库 ===")
        
        # 连接源数据库
        print(f"连接源数据库: {self.source_uri}")
        self.source_db = MilvusVectorDB(
            uri=self.source_uri, 
            token=self.source_token, 
            database=self.source_database
        )
        await self.source_db.connect()
        print("✅ 源数据库连接成功")
        
        # 连接目标数据库
        if not self.target_uri:
            print("⚠️ 目标数据库配置为空，请先配置目标数据库信息")
            return False
            
        print(f"连接目标数据库: {self.target_uri}")
        self.target_db = MilvusVectorDB(
            uri=self.target_uri, 
            token=self.target_token, 
            database=self.target_database
        )
        await self.target_db.connect()
        print("✅ 目标数据库连接成功")
        
        return True
    
    async def list_source_collections(self) -> List[str]:
        """获取源数据库中的所有集合"""
        print("\n=== 获取源数据库集合列表 ===")
        
        await self.source_db._refresh_collections()
        collections = list(self.source_db.collections_cache)
        
        print(f"发现 {len(collections)} 个集合:")
        for i, collection in enumerate(collections, 1):
            print(f"  {i}. {collection}")
            
        return collections
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合的详细信息"""
        print(f"\n=== 分析集合: {collection_name} ===")
        
        try:
            # 获取集合schema信息
            collection_info = self.source_db.client.describe_collection(collection_name)
            
            # 获取数据统计
            stats = self.source_db.client.get_collection_stats(collection_name)
            row_count = stats.get('row_count', 0)
            
            # 获取样本数据以分析结构
            sample_data = await self.source_db.search_vectors(
                collection=collection_name,
                vector=[0.1] * 384,  # 使用默认维度的零向量
                top_k=1
            )
            
            info = {
                'name': collection_name,
                'schema': collection_info,
                'row_count': row_count,
                'sample_data': sample_data[0] if sample_data else None
            }
            
            print(f"  📊 数据量: {row_count} 条")
            if sample_data:
                sample = sample_data[0]
                print(f"  📝 字段: {list(sample.keys())}")
                if 'vector' in sample:
                    print(f"  🔢 向量维度: {len(sample['vector'])}")
                    
            return info
            
        except Exception as e:
            print(f"❌ 获取集合信息失败: {e}")
            return None
    
    async def migrate_collection(self, collection_name: str, batch_size: int = 1000) -> bool:
        """迁移单个集合的数据"""
        print(f"\n=== 开始迁移集合: {collection_name} ===")
        
        try:
            # 1. 获取源集合信息
            collection_info = await self.get_collection_info(collection_name)
            if not collection_info:
                return False
                
            row_count = collection_info['row_count']
            if row_count == 0:
                print("⚠️ 集合为空，跳过迁移")
                return True
                
            # 2. 获取向量维度
            sample_data = collection_info['sample_data']
            if not sample_data or 'vector' not in sample_data:
                print("❌ 无法获取向量维度信息")
                return False
                
            vector_dim = len(sample_data['vector'])
            print(f"📏 向量维度: {vector_dim}")
            
            # 3. 在目标数据库创建集合
            print(f"🔨 在目标数据库创建集合: {collection_name}")
            await self.target_db.create_collection(collection_name, vector_dim)
            
            # 4. 分批迁移数据
            print(f"🚀 开始数据迁移，总计 {row_count} 条记录")
            
            migrated_count = 0
            offset = 0
            
            while offset < row_count:
                current_batch_size = min(batch_size, row_count - offset)
                print(f"  📦 迁移批次: {offset + 1} - {offset + current_batch_size}")
                
                # 从源数据库获取数据
                batch_data = await self.get_batch_data(collection_name, offset, current_batch_size)
                
                if not batch_data:
                    print(f"⚠️ 批次 {offset} 获取数据为空")
                    break
                
                # 写入目标数据库
                success = await self.insert_batch_data(collection_name, batch_data)
                
                if success:
                    migrated_count += len(batch_data)
                    print(f"  ✅ 已迁移: {migrated_count}/{row_count}")
                else:
                    print(f"  ❌ 批次迁移失败")
                    return False
                
                offset += current_batch_size
            
            print(f"🎉 集合 {collection_name} 迁移完成！总计迁移 {migrated_count} 条记录")
            return True
            
        except Exception as e:
            print(f"❌ 迁移集合失败: {e}")
            return False
    
    async def get_batch_data(self, collection_name: str, offset: int, limit: int) -> List[Dict[str, Any]]:
        """从源数据库获取批量数据"""
        try:
            # 使用query方法获取数据（更适合批量获取）
            results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",  # 获取所有数据
                output_fields=["*"],  # 获取所有字段
                offset=offset,
                limit=limit
            )
            
            return results
            
        except Exception as e:
            print(f"❌ 获取批量数据失败: {e}")
            return []
    
    async def insert_batch_data(self, collection_name: str, data: List[Dict[str, Any]]) -> bool:
        """将批量数据插入目标数据库"""
        try:
            # 转换数据格式以适配目标数据库
            records = []
            for item in data:
                # 移除自动生成的id字段
                if 'id' in item:
                    del item['id']
                
                # 确保metadata是字典格式
                if 'metadata' in item:
                    metadata = item['metadata']
                    if isinstance(metadata, str):
                        try:
                            metadata = json.loads(metadata)
                        except:
                            metadata = {}
                    item['metadata'] = metadata
                
                records.append(item)
            
            # 使用目标数据库的insert_vectors方法
            await self.target_db.insert_vectors(collection_name, records)
            return True
            
        except Exception as e:
            print(f"❌ 插入批量数据失败: {e}")
            return False
    
    async def verify_migration(self, collection_name: str) -> bool:
        """验证迁移结果"""
        print(f"\n=== 验证迁移结果: {collection_name} ===")
        
        try:
            # 获取源数据库记录数
            source_stats = self.source_db.client.get_collection_stats(collection_name)
            source_count = source_stats.get('row_count', 0)
            
            # 获取目标数据库记录数
            target_stats = self.target_db.client.get_collection_stats(collection_name)
            target_count = target_stats.get('row_count', 0)
            
            print(f"📊 源数据库记录数: {source_count}")
            print(f"📊 目标数据库记录数: {target_count}")
            
            if source_count == target_count:
                print("✅ 数据迁移验证成功！")
                return True
            else:
                print("❌ 数据迁移验证失败，记录数不匹配")
                return False
                
        except Exception as e:
            print(f"❌ 验证迁移结果失败: {e}")
            return False
    
    async def migrate_all_collections(self, selected_collections: List[str] = None):
        """迁移所有或指定的集合"""
        print("\n🚀 开始批量迁移...")
        
        # 获取要迁移的集合列表
        if selected_collections:
            collections = selected_collections
        else:
            collections = await self.list_source_collections()
        
        success_count = 0
        failed_collections = []
        
        for collection in collections:
            print(f"\n{'='*50}")
            success = await self.migrate_collection(collection)
            
            if success:
                # 验证迁移结果
                if await self.verify_migration(collection):
                    success_count += 1
                else:
                    failed_collections.append(collection)
            else:
                failed_collections.append(collection)
        
        # 输出迁移总结
        print(f"\n{'='*50}")
        print("🎯 迁移总结:")
        print(f"  ✅ 成功迁移: {success_count} 个集合")
        print(f"  ❌ 失败集合: {len(failed_collections)} 个")
        
        if failed_collections:
            print("  失败的集合:")
            for collection in failed_collections:
                print(f"    - {collection}")
    
    def configure_target_database(self):
        """配置目标数据库连接信息"""
        print("\n=== 配置目标数据库 ===")
        print("请输入目标Milvus数据库的连接信息:")
        
        self.target_uri = input("目标数据库URI: ").strip()
        self.target_token = input("目标数据库Token (可选): ").strip()
        self.target_database = input("目标数据库名称 (可选): ").strip()
        
        print(f"✅ 目标数据库配置完成")
        print(f"  URI: {self.target_uri}")
        print(f"  Database: {self.target_database or '默认'}")

async def main():
    """主函数"""
    print("🔄 Milvus向量数据库迁移工具")
    print("=" * 50)
    
    migrator = MilvusDataMigrator()
    
    # 配置目标数据库
    migrator.configure_target_database()
    
    # 连接数据库
    if not await migrator.connect_databases():
        print("❌ 数据库连接失败，退出程序")
        return
    
    # 获取源数据库集合列表
    collections = await migrator.list_source_collections()
    
    if not collections:
        print("⚠️ 源数据库中没有找到集合")
        return
    
    # 选择要迁移的集合
    print(f"\n请选择要迁移的集合 (输入序号，多个用逗号分隔，或输入 'all' 迁移所有):")
    choice = input("选择: ").strip()
    
    if choice.lower() == 'all':
        selected_collections = collections
    else:
        try:
            indices = [int(x.strip()) - 1 for x in choice.split(',')]
            selected_collections = [collections[i] for i in indices if 0 <= i < len(collections)]
        except:
            print("❌ 输入格式错误")
            return
    
    if not selected_collections:
        print("⚠️ 没有选择任何集合")
        return
    
    print(f"\n将迁移以下集合:")
    for collection in selected_collections:
        print(f"  - {collection}")
    
    confirm = input("\n确认开始迁移? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消迁移")
        return
    
    # 开始迁移
    await migrator.migrate_all_collections(selected_collections)
    
    print("\n🎉 迁移程序执行完成！")

if __name__ == "__main__":
    asyncio.run(main())
