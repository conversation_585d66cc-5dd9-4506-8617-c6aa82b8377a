#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus数据迁移配置文件
"""

# 源数据库配置（当前使用的数据库）
SOURCE_CONFIG = {
    "uri": "",  # 从环境变量 VECTOR_DB_URI 读取
    "token": "",  # 从环境变量 VECTOR_DB_TOKEN 读取  
    "database": "",  # 从环境变量 VECTOR_DB_DATABASE 读取
}

# 目标数据库配置（新的Milvus数据库）
TARGET_CONFIG = {
    "uri": "",  # 请填写目标Milvus的URI，例如: "https://your-target-milvus.com:19530"
    "token": "",  # 请填写目标Milvus的Token（如果需要）
    "database": "",  # 请填写目标数据库名称（可选）
}

# 迁移配置
MIGRATION_CONFIG = {
    "batch_size": 1000,  # 每批迁移的记录数
    "verify_after_migration": True,  # 迁移后是否验证数据
    "skip_existing_collections": True,  # 是否跳过已存在的集合
    "preserve_collection_names": True,  # 是否保持原集合名称
}

# 日志配置
LOGGING_CONFIG = {
    "log_level": "INFO",
    "log_file": "migration.log",
    "console_output": True,
}

# 示例目标数据库配置（请根据实际情况修改）
EXAMPLE_CONFIGS = {
    "local_milvus": {
        "uri": "http://localhost:19530",
        "token": "",
        "database": "default"
    },
    "cloud_milvus": {
        "uri": "https://your-cloud-milvus.com:19530",
        "token": "your-cloud-token",
        "database": "production"
    },
    "zilliz_cloud": {
        "uri": "https://your-cluster.zillizcloud.com:19530",
        "token": "your-zilliz-token",
        "database": "default"
    }
}
