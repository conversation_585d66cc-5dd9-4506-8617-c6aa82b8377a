#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版Milvus向量数据库迁移工具
专门处理向量维度获取问题
"""

import os
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB
from migration_config import SOURCE_CONFIG, TARGET_CONFIG, MIGRATION_CONFIG, LOGGING_CONFIG

# 加载环境变量
load_dotenv()

class FixedMilvusMigrator:
    def __init__(self):
        # 源数据库配置（优先使用配置文件，其次使用环境变量）
        self.source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
        self.source_token = SOURCE_CONFIG.get("token") or os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
        
        # 目标数据库配置（从配置文件读取）
        self.target_uri = TARGET_CONFIG.get("uri", "")
        self.target_token = TARGET_CONFIG.get("token", "")
        self.target_database = TARGET_CONFIG.get("database", "")
        
        # 迁移配置
        self.batch_size = MIGRATION_CONFIG.get("batch_size", 1000)
        self.verify_after_migration = MIGRATION_CONFIG.get("verify_after_migration", True)
        
        # 数据库连接
        self.source_db = None
        self.target_db = None
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        log_level = getattr(logging, LOGGING_CONFIG.get("log_level", "INFO"))
        log_file = LOGGING_CONFIG.get("log_file", "migration.log")
        
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        self.logger = logging.getLogger('FixedMilvusMigrator')
        self.logger.setLevel(log_level)
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 控制台处理器
        if LOGGING_CONFIG.get("console_output", True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
    
    async def connect_databases(self) -> bool:
        """连接源数据库和目标数据库"""
        self.logger.info("开始连接数据库...")
        
        try:
            # 连接源数据库
            self.logger.info(f"连接源数据库: {self.source_uri}")
            self.source_db = MilvusVectorDB(
                uri=self.source_uri, 
                token=self.source_token, 
                database=self.source_database
            )
            await self.source_db.connect()
            self.logger.info("源数据库连接成功")
            
            # 检查目标数据库配置
            if not self.target_uri:
                self.logger.error("目标数据库URI未配置")
                return False
                
            # 连接目标数据库
            self.logger.info(f"连接目标数据库: {self.target_uri}")
            self.target_db = MilvusVectorDB(
                uri=self.target_uri, 
                token=self.target_token, 
                database=self.target_database
            )
            await self.target_db.connect()
            self.logger.info("目标数据库连接成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    async def get_collections_to_migrate(self) -> List[str]:
        """获取需要迁移的集合列表"""
        await self.source_db._refresh_collections()
        all_collections = list(self.source_db.collections_cache)
        return all_collections
    
    def get_vector_dimension_from_user(self, collection_name: str) -> Optional[int]:
        """让用户手动输入向量维度"""
        print(f"\n⚠️ 需要手动输入集合 '{collection_name}' 的向量维度")
        print("常见向量维度:")
        print("  - 384  (sentence-transformers/all-MiniLM-L6-v2)")
        print("  - 768  (BERT base)")
        print("  - 1536 (OpenAI text-embedding-ada-002)")
        print("  - 3072 (OpenAI text-embedding-3-large)")
        print("  - 12288 (某些大型模型)")
        
        while True:
            try:
                dim_input = input(f"请输入 '{collection_name}' 的向量维度: ").strip()
                if dim_input:
                    dim = int(dim_input)
                    if dim > 0:
                        self.logger.info(f"用户输入向量维度: {dim}")
                        return dim
                    else:
                        print("❌ 向量维度必须是正整数")
                else:
                    print("❌ 请输入向量维度")
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                self.logger.error("用户取消输入")
                return None
    
    async def get_vector_dimension(self, collection_name: str) -> Optional[int]:
        """获取集合的向量维度"""
        try:
            # 方法1: 通过查询少量数据获取向量维度
            try:
                self.logger.info(f"尝试通过查询数据获取 {collection_name} 的向量维度")
                query_results = self.source_db.client.query(
                    collection_name=collection_name,
                    filter="",
                    output_fields=["vector"],
                    limit=1
                )
                
                if query_results and len(query_results) > 0:
                    vector = query_results[0].get('vector')
                    if vector and isinstance(vector, list):
                        dim = len(vector)
                        self.logger.info(f"通过查询数据获取到向量维度: {dim}")
                        return dim
            except Exception as e:
                self.logger.warning(f"通过查询数据获取向量维度失败: {e}")
            
            # 方法2: 让用户手动输入
            return self.get_vector_dimension_from_user(collection_name)
                    
        except Exception as e:
            self.logger.error(f"获取向量维度时发生错误: {e}")
            return None
    
    async def migrate_collection(self, collection_name: str) -> bool:
        """迁移单个集合"""
        self.logger.info(f"开始迁移集合: {collection_name}")
        
        try:
            # 1. 获取集合统计信息
            stats = self.source_db.client.get_collection_stats(collection_name)
            total_count = stats.get('row_count', 0)
            
            if total_count == 0:
                self.logger.warning(f"集合 {collection_name} 为空，跳过迁移")
                return True
            
            self.logger.info(f"集合 {collection_name} 包含 {total_count} 条记录")
            
            # 2. 获取向量维度
            vector_dim = await self.get_vector_dimension(collection_name)
            if vector_dim is None:
                self.logger.error(f"无法获取集合 {collection_name} 的向量维度")
                return False
            
            # 3. 创建目标集合
            self.logger.info(f"创建目标集合 {collection_name}，维度: {vector_dim}")
            await self.target_db.create_collection(collection_name, vector_dim)
            
            # 4. 分批迁移数据
            self.logger.info(f"开始数据迁移，总计 {total_count} 条记录")

            migrated_count = 0
            offset = 0
            current_batch_size = self.batch_size
            consecutive_failures = 0
            max_failures = 3

            while offset < total_count:
                remaining = total_count - offset
                actual_batch_size = min(current_batch_size, remaining)

                self.logger.info(f"迁移批次: {offset + 1} - {offset + actual_batch_size} (批次大小: {actual_batch_size})")

                # 获取批量数据
                batch_data = await self.get_batch_data(collection_name, offset, actual_batch_size)

                if not batch_data:
                    consecutive_failures += 1
                    self.logger.warning(f"批次 {offset} 获取数据为空 (连续失败: {consecutive_failures})")

                    if consecutive_failures >= max_failures:
                        self.logger.error(f"连续 {max_failures} 次获取数据失败，停止迁移")
                        break

                    # 减少批次大小重试
                    if current_batch_size > 10:
                        current_batch_size = max(current_batch_size // 2, 10)
                        self.logger.info(f"减少批次大小到: {current_batch_size}")
                        continue
                    else:
                        # 如果批次大小已经很小还是失败，跳过这个偏移量
                        self.logger.warning(f"跳过偏移量 {offset}")
                        offset += 1
                        continue

                # 重置失败计数
                consecutive_failures = 0

                # 插入数据
                success = await self.insert_batch_data(collection_name, batch_data)

                if success:
                    migrated_count += len(batch_data)
                    progress_percent = (migrated_count / total_count) * 100
                    self.logger.info(f"已迁移: {migrated_count}/{total_count} ({progress_percent:.1f}%)")

                    # 如果成功且批次大小较小，尝试逐渐增加
                    if current_batch_size < self.batch_size and len(batch_data) == actual_batch_size:
                        current_batch_size = min(current_batch_size * 2, self.batch_size)
                        self.logger.debug(f"增加批次大小到: {current_batch_size}")
                else:
                    self.logger.error(f"批次迁移失败，偏移量: {offset}")
                    return False

                offset += len(batch_data)  # 使用实际获取的数据量
            
            self.logger.info(f"集合 {collection_name} 迁移完成，总计: {migrated_count} 条记录")
            
            # 验证迁移结果
            if self.verify_after_migration:
                verification_result = await self.verify_migration(collection_name)
                if not verification_result:
                    # 如果自动验证失败，询问用户是否手动确认
                    print(f"\n⚠️ 集合 '{collection_name}' 自动验证失败")
                    print("可能原因: Milvus统计更新延迟、索引构建中")
                    print("请手动检查目标数据库中的数据是否正确")

                    manual_confirm = input("数据是否迁移正确? (y/N): ").strip().lower()
                    if manual_confirm == 'y':
                        self.logger.info(f"用户手动确认集合 {collection_name} 迁移成功")
                        return True
                    else:
                        self.logger.error(f"用户确认集合 {collection_name} 迁移失败")
                        return False
                else:
                    return True

            return True
            
        except Exception as e:
            self.logger.error(f"迁移集合 {collection_name} 失败: {e}")
            return False
    
    async def get_batch_data(self, collection_name: str, offset: int, limit: int) -> List[Dict[str, Any]]:
        """获取批量数据，支持字段分步查询以处理大记录"""

        # 策略1: 尝试正常查询所有字段
        try:
            self.logger.debug(f"尝试正常查询，offset: {offset}, limit: {limit}")
            results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["*"],
                offset=offset,
                limit=limit
            )
            if results:
                return results
        except Exception as e:
            if "exceed the limit size" not in str(e).lower():
                self.logger.error(f"查询失败: {e}")
                return []
            self.logger.warning("正常查询超过大小限制，尝试分步查询")

        # 策略2: 分步查询 - 先获取基本字段，再获取大字段
        try:
            # 第一步：获取除vector外的基本字段
            basic_results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["id", "content", "collection_name", "metadata"],
                offset=offset,
                limit=limit
            )

            if not basic_results:
                self.logger.warning(f"基本字段查询返回空结果，offset: {offset}")
                return []

            # 第二步：逐条获取vector字段
            complete_results = []
            for basic_record in basic_results:
                record_id = basic_record.get('id')
                if record_id is None:
                    self.logger.warning("记录缺少ID字段，跳过")
                    continue

                try:
                    # 根据ID获取vector字段
                    vector_results = self.source_db.client.query(
                        collection_name=collection_name,
                        filter=f"id == {record_id}",
                        output_fields=["vector"],
                        limit=1
                    )

                    if vector_results and len(vector_results) > 0:
                        # 合并基本字段和vector字段
                        complete_record = basic_record.copy()
                        complete_record['vector'] = vector_results[0].get('vector', [])
                        complete_results.append(complete_record)
                    else:
                        self.logger.warning(f"无法获取记录 {record_id} 的vector字段")
                        # 仍然添加记录，但vector为空
                        basic_record['vector'] = []
                        complete_results.append(basic_record)

                except Exception as ve:
                    self.logger.warning(f"获取记录 {record_id} 的vector失败: {ve}")
                    # 添加没有vector的记录
                    basic_record['vector'] = []
                    complete_results.append(basic_record)

            self.logger.info(f"分步查询成功获取 {len(complete_results)} 条记录")
            return complete_results

        except Exception as e:
            self.logger.error(f"分步查询也失败: {e}")

        # 策略3: 极限模式 - 逐条查询
        try:
            self.logger.warning("尝试极限模式：逐条查询")
            results = []

            for i in range(limit):
                current_offset = offset + i
                try:
                    # 先尝试获取基本信息
                    single_result = self.source_db.client.query(
                        collection_name=collection_name,
                        filter="",
                        output_fields=["id", "content", "collection_name"],
                        offset=current_offset,
                        limit=1
                    )

                    if not single_result:
                        break  # 没有更多数据

                    record = single_result[0].copy()

                    # 尝试获取metadata（可能很大）
                    try:
                        metadata_result = self.source_db.client.query(
                            collection_name=collection_name,
                            filter=f"id == {record.get('id')}",
                            output_fields=["metadata"],
                            limit=1
                        )
                        if metadata_result:
                            record['metadata'] = metadata_result[0].get('metadata', {})
                    except:
                        record['metadata'] = {}

                    # 尝试获取vector（通常最大）
                    try:
                        vector_result = self.source_db.client.query(
                            collection_name=collection_name,
                            filter=f"id == {record.get('id')}",
                            output_fields=["vector"],
                            limit=1
                        )
                        if vector_result:
                            record['vector'] = vector_result[0].get('vector', [])
                    except:
                        record['vector'] = []

                    results.append(record)

                except Exception as single_error:
                    self.logger.warning(f"跳过偏移量 {current_offset}: {single_error}")
                    continue

            if results:
                self.logger.info(f"极限模式成功获取 {len(results)} 条记录")
                return results

        except Exception as e:
            self.logger.error(f"极限模式也失败: {e}")

        self.logger.error(f"所有查询策略都失败，无法获取偏移量 {offset} 的数据")
        return []
    
    async def insert_batch_data(self, collection_name: str, data: List[Dict[str, Any]]) -> bool:
        """插入批量数据"""
        try:
            # 数据格式转换
            records = []
            dynamic_fields_found = set()
            
            for item in data:
                # 创建新的记录副本
                record = {}
                
                for key, value in item.items():
                    if key == 'id':
                        # 跳过自动生成的id字段
                        continue
                    elif key == 'metadata':
                        # 确保metadata是字典格式
                        if isinstance(value, str):
                            try:
                                record['metadata'] = json.loads(value)
                                self.logger.debug("成功解析字符串格式的metadata")
                            except Exception as e:
                                self.logger.warning(f"无法解析metadata字符串: {e}")
                                record['metadata'] = {}
                        else:
                            record['metadata'] = value if isinstance(value, dict) else {}
                    else:
                        # 保留所有其他字段（包括动态字段）
                        record[key] = value
                        
                        # 记录动态字段
                        if key not in ['vector', 'content', 'collection_name', 'metadata']:
                            dynamic_fields_found.add(key)
                
                records.append(record)
            
            # 输出动态字段信息
            if dynamic_fields_found:
                self.logger.info(f"发现动态字段: {list(dynamic_fields_found)}")
            
            await self.target_db.insert_vectors(collection_name, records)
            return True
            
        except Exception as e:
            self.logger.error(f"插入批量数据失败: {e}")
            return False
    
    async def verify_migration(self, collection_name: str) -> bool:
        """验证迁移结果"""
        try:
            # 获取源数据库统计
            source_stats = self.source_db.client.get_collection_stats(collection_name)
            source_count = source_stats.get('row_count', 0)

            self.logger.info(f"开始验证集合 {collection_name}，源数据库记录数: {source_count}")

            # 等待目标数据库统计更新，最多重试5次
            max_retries = 5
            wait_seconds = 2

            for attempt in range(max_retries):
                try:
                    # 刷新目标集合统计
                    self.logger.info(f"第 {attempt + 1} 次验证，等待 {wait_seconds} 秒...")
                    await asyncio.sleep(wait_seconds)

                    # 尝试刷新集合加载状态
                    try:
                        self.target_db.client.load_collection(collection_name)
                        await asyncio.sleep(1)  # 等待加载完成
                    except Exception as e:
                        self.logger.debug(f"刷新集合加载状态时出错: {e}")

                    # 获取目标数据库统计
                    target_stats = self.target_db.client.get_collection_stats(collection_name)
                    target_count = target_stats.get('row_count', 0)

                    self.logger.info(f"验证尝试 {attempt + 1}: 源:{source_count} 目标:{target_count}")

                    if source_count == target_count:
                        self.logger.info(f"✅ 验证成功: {collection_name} ({source_count} 条记录)")
                        return True
                    elif target_count > source_count:
                        self.logger.warning(f"⚠️ 目标记录数超过源记录数: {collection_name} 源:{source_count} 目标:{target_count}")
                        # 如果目标记录数更多，可能是正常的（比如有重复数据处理）
                        return True
                    else:
                        # 如果还没达到预期，继续等待
                        diff = source_count - target_count
                        self.logger.info(f"还差 {diff} 条记录，继续等待...")
                        wait_seconds = min(wait_seconds * 1.5, 10)  # 逐渐增加等待时间

                except Exception as e:
                    self.logger.warning(f"验证尝试 {attempt + 1} 失败: {e}")
                    continue

            # 最终验证
            try:
                target_stats = self.target_db.client.get_collection_stats(collection_name)
                target_count = target_stats.get('row_count', 0)

                if source_count == target_count:
                    self.logger.info(f"✅ 最终验证成功: {collection_name} ({source_count} 条记录)")
                    return True
                else:
                    diff = abs(source_count - target_count)
                    diff_percent = (diff / source_count) * 100 if source_count > 0 else 0

                    if diff_percent <= 1.0:  # 允许1%的误差
                        self.logger.warning(f"⚠️ 验证通过(允许误差): {collection_name} 源:{source_count} 目标:{target_count} 误差:{diff_percent:.2f}%")
                        return True
                    else:
                        self.logger.error(f"❌ 验证失败: {collection_name} 源:{source_count} 目标:{target_count} 误差:{diff_percent:.2f}%")

                        # 提供详细的诊断信息
                        self.logger.info("🔍 诊断信息:")
                        self.logger.info(f"  - 数据差异: {diff} 条记录")
                        self.logger.info(f"  - 可能原因: Milvus统计延迟、索引构建中、数据重复处理")
                        self.logger.info(f"  - 建议: 等待几分钟后手动检查目标数据库")

                        return False

            except Exception as e:
                self.logger.error(f"最终验证失败: {e}")
                return False

        except Exception as e:
            self.logger.error(f"验证迁移结果失败: {e}")
            return False
    
    async def run_migration(self, selected_collections: List[str] = None):
        """运行迁移任务"""
        self.logger.info("开始修复版迁移任务")
        
        # 连接数据库
        if not await self.connect_databases():
            self.logger.error("数据库连接失败，退出迁移")
            return
        
        # 获取要迁移的集合
        if selected_collections:
            collections = selected_collections
        else:
            collections = await self.get_collections_to_migrate()
        
        if not collections:
            self.logger.info("没有需要迁移的集合")
            return
        
        self.logger.info(f"将迁移 {len(collections)} 个集合: {collections}")
        
        # 开始迁移
        success_count = 0
        failed_collections = []
        
        start_time = time.time()
        
        for i, collection in enumerate(collections, 1):
            self.logger.info(f"[{i}/{len(collections)}] 处理集合: {collection}")
            
            success = await self.migrate_collection(collection)
            
            if success:
                success_count += 1
            else:
                failed_collections.append(collection)
        
        # 迁移总结
        end_time = time.time()
        duration = end_time - start_time
        
        self.logger.info("=" * 50)
        self.logger.info("迁移任务完成")
        self.logger.info(f"总耗时: {duration:.2f} 秒")
        self.logger.info(f"成功迁移: {success_count} 个集合")
        self.logger.info(f"失败集合: {len(failed_collections)} 个")
        
        if failed_collections:
            self.logger.error(f"失败的集合: {failed_collections}")

async def main():
    """主函数"""
    print("🔧 修复版Milvus向量数据库迁移工具")
    print("=" * 50)
    
    migrator = FixedMilvusMigrator()
    
    print("\n选择操作:")
    print("1. 迁移所有集合")
    print("2. 迁移指定集合")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        await migrator.run_migration()
    elif choice == "2":
        # 获取集合列表供选择
        if await migrator.connect_databases():
            await migrator.source_db._refresh_collections()
            collections = list(migrator.source_db.collections_cache)
            
            print("\n可用集合:")
            for i, col in enumerate(collections, 1):
                print(f"  {i}. {col}")
            
            selection = input("请输入集合序号 (用逗号分隔): ").strip()
            try:
                indices = [int(x.strip()) - 1 for x in selection.split(',')]
                selected = [collections[i] for i in indices if 0 <= i < len(collections)]
                await migrator.run_migration(selected)
            except:
                print("输入格式错误")
    else:
        print("无效选择")

if __name__ == "__main__":
    asyncio.run(main())
