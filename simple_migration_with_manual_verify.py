#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Milvus迁移工具，支持手动验证
专门处理统计延迟问题
"""

import os
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB
from migration_config import SOURCE_CONFIG, TARGET_CONFIG, MIGRATION_CONFIG, LOGGING_CONFIG

# 加载环境变量
load_dotenv()

class SimpleMigrator:
    def __init__(self):
        # 源数据库配置
        self.source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
        self.source_token = SOURCE_CONFIG.get("token") or os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
        
        # 目标数据库配置
        self.target_uri = TARGET_CONFIG.get("uri", "")
        self.target_token = TARGET_CONFIG.get("token", "")
        self.target_database = TARGET_CONFIG.get("database", "")
        
        # 迁移配置
        self.batch_size = MIGRATION_CONFIG.get("batch_size", 1000)
        
        # 数据库连接
        self.source_db = None
        self.target_db = None
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('simple_migration.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('SimpleMigrator')
    
    async def connect_databases(self) -> bool:
        """连接数据库"""
        try:
            # 连接源数据库
            self.logger.info(f"连接源数据库: {self.source_uri}")
            self.source_db = MilvusVectorDB(
                uri=self.source_uri, 
                token=self.source_token, 
                database=self.source_database
            )
            await self.source_db.connect()
            self.logger.info("源数据库连接成功")
            
            # 连接目标数据库
            if not self.target_uri:
                self.logger.error("目标数据库URI未配置")
                return False
                
            self.logger.info(f"连接目标数据库: {self.target_uri}")
            self.target_db = MilvusVectorDB(
                uri=self.target_uri, 
                token=self.target_token, 
                database=self.target_database
            )
            await self.target_db.connect()
            self.logger.info("目标数据库连接成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def get_vector_dimension_from_user(self, collection_name: str) -> Optional[int]:
        """让用户输入向量维度"""
        print(f"\n⚠️ 请输入集合 '{collection_name}' 的向量维度")
        print("常见维度: 384, 768, 1536, 3072, 12288")
        
        while True:
            try:
                dim_input = input(f"向量维度: ").strip()
                if dim_input:
                    dim = int(dim_input)
                    if dim > 0:
                        return dim
                    else:
                        print("❌ 向量维度必须是正整数")
                else:
                    print("❌ 请输入向量维度")
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                return None
    
    async def get_vector_dimension(self, collection_name: str) -> Optional[int]:
        """获取向量维度"""
        try:
            # 尝试通过查询获取
            query_results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["vector"],
                limit=1
            )
            
            if query_results and len(query_results) > 0:
                vector = query_results[0].get('vector')
                if vector and isinstance(vector, list):
                    dim = len(vector)
                    self.logger.info(f"自动获取到向量维度: {dim}")
                    return dim
        except Exception as e:
            self.logger.warning(f"自动获取向量维度失败: {e}")
        
        # 让用户手动输入
        return self.get_vector_dimension_from_user(collection_name)
    
    async def migrate_collection(self, collection_name: str) -> bool:
        """迁移单个集合"""
        self.logger.info(f"开始迁移集合: {collection_name}")
        
        try:
            # 1. 获取集合统计
            stats = self.source_db.client.get_collection_stats(collection_name)
            total_count = stats.get('row_count', 0)
            
            if total_count == 0:
                self.logger.warning(f"集合 {collection_name} 为空，跳过迁移")
                return True
            
            self.logger.info(f"集合 {collection_name} 包含 {total_count} 条记录")
            
            # 2. 获取向量维度
            vector_dim = await self.get_vector_dimension(collection_name)
            if vector_dim is None:
                self.logger.error(f"无法获取集合 {collection_name} 的向量维度")
                return False
            
            # 3. 创建目标集合
            self.logger.info(f"创建目标集合 {collection_name}，维度: {vector_dim}")
            await self.target_db.create_collection(collection_name, vector_dim)
            
            # 4. 分批迁移数据
            self.logger.info(f"开始数据迁移，总计 {total_count} 条记录")
            
            migrated_count = 0
            offset = 0
            
            while offset < total_count:
                current_batch_size = min(self.batch_size, total_count - offset)
                self.logger.info(f"迁移批次: {offset + 1} - {offset + current_batch_size}")
                
                # 获取批量数据
                batch_data = self.source_db.client.query(
                    collection_name=collection_name,
                    filter="",
                    output_fields=["*"],
                    offset=offset,
                    limit=current_batch_size
                )
                
                if not batch_data:
                    self.logger.warning(f"批次 {offset} 获取数据为空")
                    break
                
                # 处理数据
                records = []
                for item in batch_data:
                    record = {}
                    for key, value in item.items():
                        if key == 'id':
                            continue
                        elif key == 'metadata':
                            if isinstance(value, str):
                                try:
                                    record['metadata'] = json.loads(value)
                                except:
                                    record['metadata'] = {}
                            else:
                                record['metadata'] = value if isinstance(value, dict) else {}
                        else:
                            record[key] = value
                    records.append(record)
                
                # 插入数据
                await self.target_db.insert_vectors(collection_name, records)
                migrated_count += len(batch_data)
                
                progress_percent = (migrated_count / total_count) * 100
                self.logger.info(f"已迁移: {migrated_count}/{total_count} ({progress_percent:.1f}%)")
                
                offset += current_batch_size
            
            self.logger.info(f"集合 {collection_name} 迁移完成，总计: {migrated_count} 条记录")
            
            # 5. 手动验证
            return await self.manual_verify(collection_name, total_count, migrated_count)
            
        except Exception as e:
            self.logger.error(f"迁移集合 {collection_name} 失败: {e}")
            return False
    
    async def manual_verify(self, collection_name: str, source_count: int, migrated_count: int) -> bool:
        """手动验证迁移结果"""
        print(f"\n🔍 验证集合: {collection_name}")
        print(f"源数据库记录数: {source_count}")
        print(f"迁移记录数: {migrated_count}")
        
        # 等待一下让统计更新
        print("等待目标数据库统计更新...")
        await asyncio.sleep(3)
        
        try:
            target_stats = self.target_db.client.get_collection_stats(collection_name)
            target_count = target_stats.get('row_count', 0)
            print(f"目标数据库记录数: {target_count}")
            
            if source_count == target_count:
                print("✅ 自动验证成功！")
                return True
            else:
                diff = abs(source_count - target_count)
                print(f"⚠️ 记录数不匹配，差异: {diff} 条")
                print("可能原因:")
                print("  - Milvus统计更新延迟")
                print("  - 索引构建中")
                print("  - 数据重复处理")
                
        except Exception as e:
            print(f"⚠️ 获取目标统计失败: {e}")
        
        print(f"\n请手动检查目标数据库中集合 '{collection_name}' 的数据")
        print("您可以:")
        print("1. 登录目标Milvus查看数据量")
        print("2. 执行查询验证数据完整性")
        print("3. 等待几分钟后重新检查统计")
        
        while True:
            try:
                confirm = input("\n迁移是否成功? (y/n/r=重新检查): ").strip().lower()
                if confirm == 'y':
                    self.logger.info(f"用户确认集合 {collection_name} 迁移成功")
                    return True
                elif confirm == 'n':
                    self.logger.error(f"用户确认集合 {collection_name} 迁移失败")
                    return False
                elif confirm == 'r':
                    # 重新检查统计
                    try:
                        target_stats = self.target_db.client.get_collection_stats(collection_name)
                        target_count = target_stats.get('row_count', 0)
                        print(f"重新检查 - 目标数据库记录数: {target_count}")
                        if source_count == target_count:
                            print("✅ 重新检查验证成功！")
                            return True
                    except Exception as e:
                        print(f"重新检查失败: {e}")
                else:
                    print("请输入 y(成功)/n(失败)/r(重新检查)")
            except KeyboardInterrupt:
                print("\n用户取消验证")
                return False
    
    async def run_migration(self, collections: List[str]):
        """运行迁移"""
        if not await self.connect_databases():
            return
        
        success_count = 0
        failed_collections = []
        
        start_time = time.time()
        
        for i, collection in enumerate(collections, 1):
            print(f"\n{'='*60}")
            self.logger.info(f"[{i}/{len(collections)}] 处理集合: {collection}")
            
            success = await self.migrate_collection(collection)
            
            if success:
                success_count += 1
                print(f"✅ 集合 {collection} 迁移成功")
            else:
                failed_collections.append(collection)
                print(f"❌ 集合 {collection} 迁移失败")
        
        # 总结
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n{'='*60}")
        print("🎯 迁移总结")
        print(f"总耗时: {duration:.2f} 秒")
        print(f"成功迁移: {success_count} 个集合")
        print(f"失败集合: {len(failed_collections)} 个")
        
        if failed_collections:
            print(f"失败的集合: {failed_collections}")

async def main():
    """主函数"""
    print("🔧 简化版Milvus迁移工具（支持手动验证）")
    print("=" * 60)
    
    migrator = SimpleMigrator()
    
    # 连接数据库获取集合列表
    if not await migrator.connect_databases():
        return
    
    await migrator.source_db._refresh_collections()
    collections = list(migrator.source_db.collections_cache)
    
    if not collections:
        print("没有找到集合")
        return
    
    print(f"\n发现 {len(collections)} 个集合:")
    for i, col in enumerate(collections, 1):
        print(f"  {i}. {col}")
    
    print("\n选择操作:")
    print("1. 迁移所有集合")
    print("2. 迁移指定集合")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        await migrator.run_migration(collections)
    elif choice == "2":
        selection = input("请输入集合序号 (用逗号分隔): ").strip()
        try:
            indices = [int(x.strip()) - 1 for x in selection.split(',')]
            selected = [collections[i] for i in indices if 0 <= i < len(collections)]
            if selected:
                await migrator.run_migration(selected)
            else:
                print("没有选择有效的集合")
        except:
            print("输入格式错误")
    else:
        print("无效选择")

if __name__ == "__main__":
    asyncio.run(main())
