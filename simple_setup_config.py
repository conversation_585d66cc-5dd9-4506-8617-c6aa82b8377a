#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Milvus迁移配置设置工具
"""

import os
from pathlib import Path

def create_simple_config():
    """创建简化的配置文件"""
    print("🔧 Milvus迁移配置设置工具（简化版）")
    print("=" * 50)
    
    # 检查是否已存在配置文件
    config_file = Path("migration_config.py")
    if config_file.exists():
        print("⚠️ 配置文件 migration_config.py 已存在")
        overwrite = input("是否覆盖现有配置? (y/N): ").strip().lower()
        if overwrite != 'y':
            print("❌ 用户取消操作")
            return
    
    print("\n=== 配置目标数据库 ===")
    print("⚠️ 必须填写目标数据库URI")
    
    target_uri = input("目标数据库URI: ").strip()
    if not target_uri:
        print("❌ 目标数据库URI不能为空")
        return
    
    target_token = input("目标数据库Token (可选): ").strip()
    target_database = input("目标数据库名称 (可选): ").strip()
    
    print("\n=== 配置迁移参数 ===")
    
    # 批次大小
    batch_size_input = input("批次大小 (默认1000): ").strip()
    try:
        batch_size = int(batch_size_input) if batch_size_input else 1000
    except ValueError:
        batch_size = 1000
        print("⚠️ 批次大小格式错误，使用默认值1000")
    
    # 验证迁移
    verify_input = input("迁移后验证数据 (Y/n): ").strip().lower()
    verify_migration = verify_input != 'n'
    
    # 跳过已存在的集合
    skip_input = input("跳过已存在的集合 (Y/n): ").strip().lower()
    skip_existing = skip_input != 'n'
    
    # 生成配置文件内容（使用简单的字符串拼接，避免f-string问题）
    config_lines = [
        '#!/usr/bin/env python3',
        '# -*- coding: utf-8 -*-',
        '"""',
        'Milvus数据迁移配置文件',
        '由 simple_setup_config.py 自动生成',
        '"""',
        '',
        '# 源数据库配置（当前使用的数据库）',
        '# 如果留空，将从环境变量 VECTOR_DB_URI, VECTOR_DB_TOKEN, VECTOR_DB_DATABASE 读取',
        'SOURCE_CONFIG = {',
        '    "uri": "",  # 可选：源Milvus的URI，留空则从环境变量读取',
        '    "token": "",  # 可选：源Milvus的Token，留空则从环境变量读取',
        '    "database": "",  # 可选：源数据库名称，留空则从环境变量读取',
        '}',
        '',
        '# 目标数据库配置（新的Milvus数据库）',
        'TARGET_CONFIG = {',
        f'    "uri": "{target_uri}",  # 目标Milvus的URI',
        f'    "token": "{target_token}",  # 目标Milvus的Token（如果需要认证）',
        f'    "database": "{target_database}",  # 目标数据库名称（留空使用默认数据库）',
        '}',
        '',
        '# 迁移配置',
        'MIGRATION_CONFIG = {',
        f'    "batch_size": {batch_size},  # 每批迁移的记录数',
        f'    "verify_after_migration": {verify_migration},  # 迁移后是否验证数据',
        f'    "skip_existing_collections": {skip_existing},  # 是否跳过已存在的集合',
        '    "preserve_collection_names": True,  # 是否保持原集合名称',
        '}',
        '',
        '# 日志配置',
        'LOGGING_CONFIG = {',
        '    "log_level": "INFO",',
        '    "log_file": "migration.log",',
        '    "console_output": True,',
        '}',
        '',
        '# 配置验证函数',
        'def validate_config():',
        '    """验证配置是否正确"""',
        '    errors = []',
        '    warnings = []',
        '    ',
        '    # 检查目标配置',
        '    if not TARGET_CONFIG.get("uri"):',
        '        errors.append("TARGET_CONFIG 中的 uri 不能为空")',
        '    ',
        '    # 检查源配置',
        '    import os',
        '    source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")',
        '    if not source_uri:',
        '        warnings.append("源数据库URI未配置，请确保环境变量 VECTOR_DB_URI 已设置")',
        '    ',
        '    # 检查批次大小',
        '    batch_size = MIGRATION_CONFIG.get("batch_size", 0)',
        '    if not isinstance(batch_size, int) or batch_size <= 0:',
        '        errors.append("MIGRATION_CONFIG 中的 batch_size 必须是正整数")',
        '    ',
        '    # 输出结果',
        '    if errors:',
        '        print("❌ 配置验证失败:")',
        '        for error in errors:',
        '            print(f"  - {error}")',
        '    ',
        '    if warnings:',
        '        print("⚠️ 配置警告:")',
        '        for warning in warnings:',
        '            print(f"  - {warning}")',
        '    ',
        '    if not errors and not warnings:',
        '        print("✅ 配置验证通过")',
        '    ',
        '    return len(errors) == 0',
        '',
        'def show_current_config():',
        '    """显示当前配置"""',
        '    import os',
        '    ',
        '    print("=== 当前配置 ===")',
        '    ',
        '    # 源数据库配置',
        '    source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")',
        '    source_db = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")',
        '    print(f"源数据库:")',
        '    print(f"  URI: {source_uri or \'未配置\'}")',
        '    print(f"  Database: {source_db or \'默认\'}")',
        '    ',
        '    # 目标数据库配置',
        '    print(f"目标数据库:")',
        '    print(f"  URI: {TARGET_CONFIG.get(\'uri\') or \'未配置\'}")',
        '    print(f"  Database: {TARGET_CONFIG.get(\'database\') or \'默认\'}")',
        '    ',
        '    # 迁移配置',
        '    print(f"迁移设置:")',
        '    print(f"  批次大小: {MIGRATION_CONFIG.get(\'batch_size\')}")',
        '    print(f"  验证迁移: {MIGRATION_CONFIG.get(\'verify_after_migration\')}")',
        '    print(f"  跳过已存在: {MIGRATION_CONFIG.get(\'skip_existing_collections\')}")',
        '',
        'if __name__ == "__main__":',
        '    print("🔧 Milvus迁移配置验证工具")',
        '    print("=" * 50)',
        '    ',
        '    show_current_config()',
        '    print()',
        '    validate_config()',
    ]
    
    # 写入配置文件
    try:
        with open("migration_config.py", "w", encoding="utf-8") as f:
            f.write('\n'.join(config_lines))
        
        print("\n✅ 配置文件创建成功！")
        print(f"📁 文件位置: {Path('migration_config.py').absolute()}")
        
        print("\n=== 下一步操作 ===")
        print("1. 验证配置:")
        print("   python migration_config.py")
        print("2. 运行迁移脚本:")
        print("   python advanced_migration.py")
        
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")

def show_help():
    """显示帮助信息"""
    print("🔧 简化版Milvus迁移配置设置工具")
    print("=" * 50)
    print("此工具帮助您快速创建 migration_config.py 配置文件")
    print()
    print("使用方法:")
    print("  python simple_setup_config.py")
    print()
    print("配置说明:")
    print("- 源数据库配置会自动从环境变量读取")
    print("- 只需要配置目标数据库信息")
    print("- 迁移参数可以使用默认值")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        show_help()
    else:
        create_simple_config()
