



from openai import OpenAI

client = OpenAI(
    api_key="sk-5c8f5b7498814318b2593c5a78ba30d5",  # 如果您没有配置环境变量，请在此处用您的API Key进行替换
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"  # 百炼服务的base_url
)

completion = client.embeddings.create(
    model="text-embedding-v4",
    input='衣服的质量杠杠的，很漂亮，不枉我等了这么久啊，喜欢，以后还来这里买',
    dimensions=2048, # 指定向量维度（仅 text-embedding-v3及 text-embedding-v4支持该参数）
    encoding_format="float"
)

# print(completion.model_dump_json())
print(completion.data[0].embedding)


# import json
# embedding = json.loads(completion.model_dump_json())['data'][0]['embedding']
# print(len(embedding))