from fastapi import APIRouter, Depends, File, UploadFile, Form, HTTPException, Request
from fastapi.params import Form
from uuid import uuid4
from app.core.embedding import EmbeddingModel
from app.core.vectordb import VectorDatabase
from app.config.settings import ModelConfig, VectorDBConfig
from typing import List, Dict, Any, Optional, Union
from app.dependencies import get_embed_model_sync
from pydantic import BaseModel, Field
from pydantic.json import pydantic_encoder
from app.core.knowledge_processing import parse_pdf, parse_txt, parse_docx
from app.core.encryption import encrypt_text, decrypt_text
import time
import sys
import json
import traceback
import asyncio

async def get_embed_model(cfg: ModelConfig) -> EmbeddingModel:
    if cfg.model_type == 'huggingface':
        from app.core.embedding import HuggingFaceEmbedding
        return HuggingFaceEmbedding(cfg.model_name, cfg.device, cfg.model_path, cfg.cache_dir)
    # 其他模型实现...

async def get_vector_db(cfg: VectorDBConfig, database: str = None, create_if_not_exists: bool = False) -> VectorDatabase:
    connect_start = time.time()
    if cfg.db_type == 'milvus':
        from app.core.vectordb import MilvusVectorDB

        print(f"[连接] 使用URI方式连接Milvus: {cfg.uri}")
        if database:
            print(f"[连接] 使用数据库: {database}")
        else:
            print("[连接] 使用默认数据库")

        # 创建实例的时间
        instance_start = time.time()
        db_instance = MilvusVectorDB(
            uri=cfg.uri,
            token=cfg.token if cfg.token and len(cfg.token.strip()) > 0 else None,
            database=database
        )
        instance_time = time.time() - instance_start
        print(f"[时间] 创建Milvus实例耗时: {instance_time:.3f}秒")

        # 连接到Milvus数据库
        try:
            connect_db_start = time.time()
            await db_instance.connect()
            connect_db_time = time.time() - connect_db_start
            print(f"[成功] 成功连接到Milvus数据库, 耗时: {connect_db_time:.3f}秒")
            print(f"[时间] 数据库连接详情:")
            print(f"  - 创建实例: {instance_time:.3f}秒")
            print(f"  - 建立连接: {connect_db_time:.3f}秒")
            print(f"  - 总连接时间: {time.time() - connect_start:.3f}秒")
        except Exception as conn_error:
            if create_if_not_exists and database and "database not found" in str(conn_error).lower():
                try:
                    print(f"[信息] 数据库 '{database}' 不存在，尝试创建...")
                    # 先连接到默认数据库
                    default_db_instance = MilvusVectorDB(
                        uri=cfg.uri,
                        token=cfg.token if cfg.token and len(cfg.token.strip()) > 0 else None
                    )
                    await default_db_instance.connect()

                    # 创建新数据库
                    await default_db_instance.create_database(database)
                    print(f"[成功] 成功创建数据库 '{database}'")

                    # 重新连接到新创建的数据库
                    await db_instance.connect()
                    print(f"[成功] 成功连接到新创建的数据库 '{database}'")
                except Exception as create_error:
                    print(f"[错误] 创建数据库失败: {create_error}")
                    raise Exception(f"创建数据库失败: {create_error}")
            else:
                print(f"[错误] Milvus数据库连接失败: {conn_error}")
                raise Exception(f"Milvus连接失败: {conn_error}")

        return db_instance
    # 其他数据库实现...

class SearchResult(BaseModel):
    content: str
    similarity: float
    images: List[str] = Field(default_factory=list)

router = APIRouter(prefix="/api/v1")

class ChunkStrategy(BaseModel):
    chunk_size: str = "500"
    chunk_overlap: str = "50"

@router.post("/upload",
    response_model=dict,
    tags=["Document Processing"],
    summary="上传并处理文档"
)
async def upload_document(
    file: UploadFile = File(..., description="上传的文档文件"),
    collection: str = Form("documents", description="文档所属的集合名称，用于区分不同业务场景"),
    database: str = Form(None, description="要使用的数据库名称，不指定则使用默认数据库"),
    chunk_size: str = Form("500", description="分块大小"),
    chunk_overlap: str = Form("50", description="分块重叠大小"),
    encrypt: bool = Form(False, description="是否对上传内容进行AES加密"),
    model: EmbeddingModel = Depends(get_embed_model_sync)
):

    # 文件类型检测
    content = await file.read()
    file_type = file.filename.split('.')[-1].lower()

    # 内容格式验证
    try:
        if file_type == 'pdf':
            if not content.startswith(b'%PDF-'):
                raise ValueError("Invalid PDF header")
            text = parse_pdf(content)
        elif file_type == 'docx':
            if not content.startswith(b'PK\x03\x04'):
                raise ValueError("Invalid DOCX header")
            text = parse_docx(content)
        elif file_type == 'txt':
            text = parse_txt(content)
        else:
            raise HTTPException(400, "不支持的文档类型")

        if not text.strip():
            raise HTTPException(400, "文档内容解析为空")
    except Exception as e:
        raise HTTPException(400, f"文档解析失败: {str(e)}")

    # 将字符串参数转换为整数
    try:
        # 处理可能的特殊字符
        chunk_size_str = chunk_size.strip().split('?')[0]  # 移除可能的问号或其他特殊字符
        chunk_overlap_str = chunk_overlap.strip().split('?')[0]

        # 转换为整数
        chunk_size_int = int(chunk_size_str)
        chunk_overlap_int = int(chunk_overlap_str)

        print(f"[参数处理] 分块大小: {chunk_size} -> {chunk_size_int}")
        print(f"[参数处理] 分块重叠: {chunk_overlap} -> {chunk_overlap_int}")

        # 验证参数范围
        if chunk_size_int <= 0:
            raise ValueError("分块大小必须大于0")
        if chunk_overlap_int < 0:
            raise ValueError("分块重叠必须大于等于0")
        if chunk_overlap_int >= chunk_size_int:
            raise ValueError("分块重叠必须小于分块大小")
    except ValueError as e:
        raise HTTPException(400, f"参数错误: {str(e)}")

    # 文档分块
    chunks = [
        text[i:i+chunk_size_int]
        for i in range(0, len(text), chunk_size_int - chunk_overlap_int)
    ]

    # 生成文档ID
    doc_id = str(uuid4())

    print(f"\n\n开始处理文档: {file.filename}")
    print(f"文档ID: {doc_id}")
    print(f"集合名称: {collection}")
    print(f"总分块数: {len(chunks)}")
    print(f"分块大小: {chunk_size_int}, 重叠大小: {chunk_overlap_int}")
    print(f"是否加密: {encrypt}")

    # 向量生成和存储
    start_time = time.time()
    vectors = []

    # 获取向量数据库实例
    from app.dependencies import get_vectordb_config_sync
    cfg = get_vectordb_config_sync()
    db = await get_vector_db(cfg, database)

    # 如果指定了数据库，打印信息
    if database:
        print(f"使用数据库: {database}")
    else:
        print("使用默认数据库")

    # 创建集合
    print(f"创建向量集合: {collection}")
    await db.create_collection(collection, model.get_dimension())

    # 处理每个分块
    print("开始向量化处理...")
    for i, chunk in enumerate(chunks):
        # 显示进度
        progress = f"[{i+1}/{len(chunks)}] {((i+1)/len(chunks)*100):.1f}%"
        sys.stdout.write(f"\r处理进度: {progress}")
        sys.stdout.flush()

        # 生成向量
        vec = await model.agenerate(chunk)
        vectors.append(vec)

        # 每10个分块或最后一个分块时存储
        if (i + 1) % 10 == 0 or i == len(chunks) - 1:
            batch_start = max(0, i - 9 if (i + 1) % 10 == 0 else i)
            batch_chunks = chunks[batch_start:i+1]
            batch_vectors = vectors[batch_start:i+1]

            # 存储向量
            # 如果需要加密，对每个分块进行加密
            records = []
            for idx, (c, v) in enumerate(zip(batch_chunks, batch_vectors), start=batch_start):
                content = c
                if encrypt:
                    try:
                        print(f"[详细] 对分块 {idx+1} 进行AES加密")
                        content = encrypt_text(c)
                        print(f"[详细] 加密成功，加密后长度: {len(content)}")
                    except Exception as e:
                        print(f"[错误] 加密分块 {idx+1} 时出错: {e}")
                        print(f"[错误] 异常类型: {type(e)}")
                        print(f"[错误] 异常堆栈: {traceback.format_exc()}")
                        raise HTTPException(500, f"加密文本时出错: {str(e)}")

                records.append({
                    "content": content,
                    "vector": v,
                    "metadata": {
                        "doc_id": doc_id,
                        "chunk_index": idx,
                        "collection": collection,
                        "encrypted": encrypt  # 添加加密标记
                    }
                })

            # 存储向量
            await db.insert_vectors(collection, records)

            # 显示批次存储信息
            if (i + 1) % 10 == 0:
                sys.stdout.write(f" - 已存储批次 {(i+1)//10}")
                sys.stdout.flush()

    # 完成后打印汇总信息
    total_time = time.time() - start_time
    print(f"\n\n向量化完成!")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均每块耗时: {total_time/len(chunks):.2f}秒")
    print(f"向量维度: {model.get_dimension()}")

    return {
        "doc_id": doc_id,
        "chunk_count": len(chunks),
        "vector_dimension": model.get_dimension()
    }



# 依赖注入函数


class EmbeddingRequest(BaseModel):
    text: str

@router.post("/embeddings")
async def create_embedding(
    request: EmbeddingRequest,
    model: EmbeddingModel = Depends(get_embed_model_sync, use_cache=True)
):
    text = request.text
    print(f"\n生成文本向量: '{text[:50]}{'...' if len(text) > 50 else ''}'")
    start_time = time.time()

    # 生成向量
    vector = await model.agenerate(text)

    # 打印结果信息
    total_time = time.time() - start_time
    print(f"向量生成完成, 维度: {len(vector)}, 耗时: {total_time:.3f}秒")

    return {"text": text, "vector": vector}


class TextUploadRequest(BaseModel):
    texts: List[str] = Field(..., description="要上传的文本列表")
    collection: str = Field("documents", description="文档所属的集合名称，用于区分不同业务场景")
    database: Optional[str] = Field(None, description="要使用的数据库名称，不指定则使用默认数据库")
    metadata: Optional[Dict[str, Any]] = Field(None, description="要附加到所有文本的元数据")
    encrypt: bool = Field(False, description="是否对上传内容进行AES加密")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'azure-openai'，不指定则使用默认方法")

    # 允许任何额外字段，以支持不同业务场景的动态字段
    # 例如：car_type, model_year, department, employee_id等
    # 这些字段会自动添加到metadata中
    class Config:
        extra = "allow"  # 允许任何额外字段
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True

@router.post("/upload_texts",
    response_model=dict,
    tags=["Document Processing"],
    summary="上传文本列表并处理为向量"
)
async def upload_texts(
    request: TextUploadRequest,
    model: EmbeddingModel = Depends(get_embed_model_sync)
):
    # 如果指定了embedding_type，使用指定的向量模型
    if request.embedding_type:
        print(f"[模型] 使用指定的向量模型类型: {request.embedding_type}")
        try:
            from app.dependencies import get_embed_model_sync
            model = await get_embed_model_sync(request.embedding_type)
            print(f"[模型] 成功加载指定的向量模型: {request.embedding_type}, 维度: {model.get_dimension()}")
        except Exception as e:
            print(f"[错误] 加载指定向量模型失败: {e}")
            raise HTTPException(400, f"加载指定向量模型失败: {str(e)}")
    texts = request.texts
    collection = request.collection
    database = request.database
    metadata = request.metadata or {}
    encrypt = request.encrypt

    # 收集所有额外字段（动态字段）
    # 获取所有标准字段名称
    standard_fields = ["texts", "collection", "database", "metadata", "encrypt", "embedding_type"]

    # 创建一个字典来存储额外字段
    extra_fields = {}

    # 使用 .model_dump() 方法获取所有字段，包括额外字段
    try:
        # 尝试使用新的 model_dump 方法（Pydantic v2）
        all_fields = request.model_dump(exclude_unset=True)
    except AttributeError:
        # 如果失败，回退到旧的 dict 方法（Pydantic v1）
        all_fields = request.dict(exclude_unset=True)
    print(f"[调试] 所有字段: {all_fields}")

    # 遍历所有字段
    for field, value in all_fields.items():
        # 如果不是标准字段且值不为None，则收集为额外字段
        if field not in standard_fields and value is not None:
            extra_fields[field] = value
            print(f"[动态字段] 收集字段 '{field}' = {value} 作为独立列")

    print(f"[元数据] 完整元数据: {metadata}")

    if not texts:
        raise HTTPException(400, "文本列表不能为空")

    # 生成文档ID
    doc_id = str(uuid4())

    print(f"\n\n开始处理文本列表")
    print(f"文档ID: {doc_id}")
    print(f"集合名称: {collection}")
    print(f"文本数量: {len(texts)}")
    print(f"是否加密: {encrypt}")

    # 向量生成和存储
    start_time = time.time()
    vectors = []

    # 获取向量数据库实例
    from app.dependencies import get_vectordb_config_sync
    cfg = get_vectordb_config_sync()

    # 尝试连接到数据库，如果不存在则创建
    try:
        print(f"[连接] 尝试连接到数据库: {database if database else '默认数据库'}")
        db = await get_vector_db(cfg, database, create_if_not_exists=True)
        print(f"[成功] 成功连接到数据库: {database if database else '默认数据库'}")
    except Exception as db_error:
        print(f"[错误] 连接或创建数据库失败: {db_error}")
        raise HTTPException(500, f"连接或创建数据库失败: {str(db_error)}")

    # 如果指定了数据库，打印信息
    if database:
        print(f"使用数据库: {database}")
    else:
        print("使用默认数据库")

    # 创建集合（如果不存在）
    try:
        print(f"[创建] 尝试创建向量集合: {collection}")
        await db.create_collection(collection, model.get_dimension())
        print(f"[成功] 向量集合已就绪: {collection}")
    except Exception as coll_error:
        print(f"[错误] 创建集合失败: {coll_error}")
        raise HTTPException(500, f"创建集合失败: {str(coll_error)}")

    # 处理每个文本
    print("开始向量化处理...")
    for i, text in enumerate(texts):
        # 显示进度
        progress = f"[{i+1}/{len(texts)}] {((i+1)/len(texts)*100):.1f}%"
        sys.stdout.write(f"\r处理进度: {progress}")
        sys.stdout.flush()

        try:
            # 生成向量
            print(f"\n[详细] 正在为文本 {i+1} 生成向量: '{text[:30]}...'")
            vec = await model.agenerate(text)
            print(f"[详细] 向量生成成功，维度: {len(vec)}")
            vectors.append(vec)
        except Exception as e:
            print(f"\n[错误] 生成向量时出错: {e}")
            print(f"[错误] 异常类型: {type(e)}")
            print(f"[错误] 异常堆栈: {traceback.format_exc()}")
            raise HTTPException(500, f"生成向量时出错: {str(e)}")

        # 立即存储每条文本的向量
        try:
            # 准备记录
            # 如果需要加密，对文本内容进行加密
            content = text
            if encrypt:
                try:
                    print(f"[详细] 对文本 {i+1} 进行AES加密")
                    content = encrypt_text(text)
                    print(f"[详细] 加密成功，加密后长度: {len(content)}")
                    # 在元数据中标记该内容已加密
                    metadata["encrypted"] = True
                except Exception as e:
                    print(f"[错误] 加密文本时出错: {e}")
                    print(f"[错误] 异常类型: {type(e)}")
                    print(f"[错误] 异常堆栈: {traceback.format_exc()}")
                    raise HTTPException(500, f"加密文本时出错: {str(e)}")

            # 创建基本记录
            record = {
                "content": content,
                "vector": vec,
                "metadata": {
                    "doc_id": doc_id,
                    "text_index": i,
                    "collection": collection,
                    "encrypted": encrypt,  # 添加加密标记
                    **metadata
                }
            }

            # 添加所有额外字段作为独立列
            for field, value in extra_fields.items():
                record[field] = value

            print(f"\n[详细] 准备存储文本 {i+1}")
            print(f"[详细] 记录准备完成，开始插入向量数据库...")
            # 存储向量
            await db.insert_vectors(collection, [record])
            print(f"[详细] 成功插入文本 {i+1} 到集合 '{collection}'")
        except Exception as e:
            print(f"\n[错误] 存储向量时出错: {e}")
            print(f"[错误] 异常类型: {type(e)}")
            print(f"[错误] 异常堆栈: {traceback.format_exc()}")
            raise HTTPException(500, f"存储向量时出错: {str(e)}")

    # 完成后打印汇总信息
    total_time = time.time() - start_time
    print(f"\n\n向量化完成!")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均每条文本耗时: {total_time/len(texts):.2f}秒")
    print(f"向量维度: {model.get_dimension()}")

    return {
        "doc_id": doc_id,
        "text_count": len(texts),
        "vector_dimension": model.get_dimension()
    }

# 定义请求体模型
class SearchRequest(BaseModel):
    text: str = Field(..., description="搜索查询文本")
    top_k: int = Field(5, description="返回结果数量")
    collection: str = Field("documents", description="要搜索的集合名称，可以设置为'all'以搜索所有集合")
    database: str = Field(None, description="要使用的数据库名称，不指定则使用默认数据库")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'openai'，不指定则使用默认方法")
    filter_expr: Optional[str] = Field(None, description="Milvus筛选表达式，例如：\"car_type='passenger'\" 或 \"metadata['category'] == 'electronics'\"")
    metadata_filters: Optional[Dict[str, Any]] = Field(None, description="metadata字段筛选条件，例如：{\"category\": \"electronics\", \"price\": {\"$lt\": 100}}")

    class Config:
        # 允许额外字段
        extra = "allow"
        # 允许从ORM模型创建
        orm_mode = True
        # 允许使用别名
        allow_population_by_field_name = True
        # 验证赋值
        validate_assignment = True

# 定义数据库搜索配置
class DBSearchConfig(BaseModel):
    database: str = Field(..., description="要搜索的数据库名称")
    collections: Optional[List[str]] = Field(None, description="要搜索的集合列表，不指定则搜索该数据库下所有集合")

# 定义多数据库搜索请求体模型
class MultiDBSearchRequest(BaseModel):
    text: str = Field(..., description="搜索查询文本")
    top_k: int = Field(5, description="每个数据库返回的结果数量")
    databases: List[Union[str, DBSearchConfig]] = Field(..., description="要搜索的数据库列表，可以是数据库名称字符串或包含数据库名称和集合列表的对象")
    total_results: int = Field(10, description="最终返回的总结果数量")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'openai'，不指定则使用默认方法")
    filter_expr: Optional[str] = Field(None, description="Milvus筛选表达式，例如：\"car_type='passenger'\" 或 \"metadata['category'] == 'electronics'\"")
    metadata_filters: Optional[Dict[str, Any]] = Field(None, description="metadata字段筛选条件，例如：{\"category\": \"electronics\", \"price\": {\"$lt\": 100}}")

    class Config:
        # 允许额外字段
        extra = "allow"
        # 允许从ORM模型创建
        orm_mode = True
        # 允许使用别名
        allow_population_by_field_name = True
        # 验证赋值
        validate_assignment = True

# 记录请求信息的函数
def log_request_info(request_obj: Any, raw_request: Request = None) -> None:
    """记录请求的详细信息"""
    print("\n" + "=" * 50)
    print("[请求信息] 开始记录请求详情")

    # 记录Pydantic模型信息
    if hasattr(request_obj, "__dict__"):
        try:
            print(f"[请求信息] Pydantic模型: {type(request_obj).__name__}")
            print(f"[请求信息] 模型字段:")
            for field_name, field_value in request_obj.__dict__.items():
                print(f"  - {field_name}: {field_value} (类型: {type(field_value).__name__})")

            # 尝试序列化为JSON
            try:
                json_data = json.dumps(request_obj.dict(), default=pydantic_encoder, ensure_ascii=False)
                print(f"[请求信息] JSON序列化结果: {json_data}")
            except Exception as json_err:
                print(f"[请求信息] JSON序列化失败: {json_err}")
        except Exception as e:
            print(f"[请求信息] 处理Pydantic模型时出错: {e}")

    # 记录原始请求信息
    if raw_request:
        try:
            print(f"[请求信息] 请求方法: {raw_request.method}")
            print(f"[请求信息] 请求URL: {raw_request.url}")
            print(f"[请求信息] 请求头:")
            for header_name, header_value in raw_request.headers.items():
                print(f"  - {header_name}: {header_value}")
        except Exception as e:
            print(f"[请求信息] 处理原始请求时出错: {e}")

    print("[请求信息] 请求详情记录完成")
    print("=" * 50)

def build_metadata_filter_expr(metadata_filters: Dict[str, Any]) -> str:
    """构建metadata字段的筛选表达式

    Args:
        metadata_filters: metadata筛选条件字典
        例如: {"category": "electronics", "price": {"$lt": 100}, "in_stock": True}

    Returns:
        str: Milvus筛选表达式
        例如: "metadata[\"category\"] == \"electronics\" and metadata[\"price\"] < 100 and metadata[\"in_stock\"] == true"
    """
    if not metadata_filters:
        return ""

    conditions = []

    for key, value in metadata_filters.items():
        if isinstance(value, dict):
            # 处理操作符，如 {"$lt": 100, "$gt": 50}
            for op, op_value in value.items():
                if op == "$lt":
                    conditions.append(f"metadata[\"{key}\"] < {op_value}")
                elif op == "$lte":
                    conditions.append(f"metadata[\"{key}\"] <= {op_value}")
                elif op == "$gt":
                    conditions.append(f"metadata[\"{key}\"] > {op_value}")
                elif op == "$gte":
                    conditions.append(f"metadata[\"{key}\"] >= {op_value}")
                elif op == "$eq":
                    if isinstance(op_value, str):
                        conditions.append(f"metadata[\"{key}\"] == \"{op_value}\"")
                    else:
                        conditions.append(f"metadata[\"{key}\"] == {str(op_value).lower()}")
                elif op == "$ne":
                    if isinstance(op_value, str):
                        conditions.append(f"metadata[\"{key}\"] != \"{op_value}\"")
                    else:
                        conditions.append(f"metadata[\"{key}\"] != {str(op_value).lower()}")
                elif op == "$in":
                    # 处理 in 操作符
                    if isinstance(op_value, list):
                        in_values = []
                        for v in op_value:
                            if isinstance(v, str):
                                in_values.append(f"\"{v}\"")
                            else:
                                in_values.append(str(v).lower())
                        conditions.append(f"metadata[\"{key}\"] in [{', '.join(in_values)}]")
        else:
            # 直接值比较
            if isinstance(value, str):
                conditions.append(f"metadata[\"{key}\"] == \"{value}\"")
            elif isinstance(value, bool):
                conditions.append(f"metadata[\"{key}\"] == {str(value).lower()}")
            elif isinstance(value, (int, float)):
                conditions.append(f"metadata[\"{key}\"] == {value}")
            else:
                # 其他类型转为字符串
                conditions.append(f"metadata[\"{key}\"] == \"{str(value)}\"")

    return " and ".join(conditions)

@router.post("/search")
async def vector_search(
    request: SearchRequest,
    raw_request: Request,
    model: EmbeddingModel = Depends(get_embed_model_sync, use_cache=True)
):
    # 如果指定了embedding_type，使用指定的向量模型
    if request.embedding_type:
        print(f"[模型] 使用指定的向量模型类型: {request.embedding_type}")
        try:
            from app.dependencies import get_embed_model_sync
            model = await get_embed_model_sync(request.embedding_type)
            print(f"[模型] 成功加载指定的向量模型: {request.embedding_type}, 维度: {model.get_dimension()}")
        except Exception as e:
            print(f"[错误] 加载指定向量模型失败: {e}")
            raise HTTPException(400, f"加载指定向量模型失败: {str(e)}")
    # 添加请求体调试日志
    print(f"\n[DEBUG] 收到搜索请求: {request}")
    print(f"[DEBUG] 请求类型: {type(request)}")
    print(f"[DEBUG] 请求字段: text={request.text}, top_k={request.top_k}, collection={request.collection}, database={request.database}")

    # 记录详细的请求信息
    log_request_info(request, raw_request)

    # 记录原始请求体
    try:
        body = await raw_request.body()
        body_str = body.decode('utf-8', errors='replace')
        print(f"[DEBUG] 原始请求体: {body_str}")
        print(f"[DEBUG] 请求体长度: {len(body)} 字节")
        print(f"[DEBUG] Content-Type: {raw_request.headers.get('content-type')}")

        # 尝试解析JSON
        try:
            json_data = json.loads(body_str)
            print(f"[DEBUG] JSON解析结果: {json_data}")
            print(f"[DEBUG] JSON类型: {type(json_data)}")

            # 检查字段类型
            if 'top_k' in json_data:
                print(f"[DEBUG] top_k字段类型: {type(json_data['top_k'])}")
            if 'collection' in json_data:
                print(f"[DEBUG] collection字段类型: {type(json_data['collection'])}")
            if 'database' in json_data:
                print(f"[DEBUG] database字段类型: {type(json_data['database'])}")
        except json.JSONDecodeError as json_err:
            print(f"[DEBUG] JSON解析失败: {json_err}")
            print(f"[DEBUG] JSON错误位置: 第 {json_err.lineno} 行, 第 {json_err.colno} 列")
            print(f"[DEBUG] JSON错误字符: '{json_err.doc[max(0, json_err.pos-20):json_err.pos]}[HERE>{json_err.doc[json_err.pos:min(len(json_err.doc), json_err.pos+20)]}]'")
    except Exception as e:
        print(f"[DEBUG] 无法读取原始请求体: {e}")
        print(f"[DEBUG] 异常类型: {type(e)}")
        print(f"[DEBUG] 异常堆栈: {traceback.format_exc()}")

    text = request.text
    collection = request.collection

    # 验证top_k参数
    top_k = request.top_k
    try:
        # 验证参数类型和范围
        print(f"[参数处理] top_k原始值: {top_k}, 类型: {type(top_k)}")

        # 如果是字符串，尝试转换为整数
        if isinstance(top_k, str):
            try:
                top_k = int(top_k)
                print(f"[参数处理] top_k字符串转换为整数: {top_k}")
            except ValueError as ve:
                print(f"[参数处理] top_k字符串转换失败: {ve}")
                raise ValueError(f"top_k必须是整数，无法将'{top_k}'转换为整数")

        # 验证参数范围
        if top_k <= 0:
            raise ValueError("top_k必须大于0")
        print(f"[参数处理] top_k最终值: {top_k}")
    except ValueError as e:
        print(f"[参数处理] top_k验证失败: {e}")
        print(f"[参数处理] 异常堆栈: {traceback.format_exc()}")
        raise HTTPException(400, f"参数错误: {str(e)}")

    print(f"\n执行向量搜索: '{text}'")
    print(f"集合: {collection}, Top-K: {top_k}")
    start_time = time.time()
    print(f"[时间] 搜索开始时间: {time.strftime('%H:%M:%S.%f')[:-3]}")

    # 生成查询向量
    print("生成查询向量...")
    vector_start = time.time()
    vector = await model.agenerate(text)
    vector_time = time.time() - vector_start
    print(f"向量生成完成, 维度: {len(vector)}, 耗时: {vector_time:.3f}秒")
    print(f"[时间] 向量生成阶段: {vector_time:.3f}秒")

    # 获取向量数据库实例
    from app.dependencies import get_vectordb_config_sync
    cfg = get_vectordb_config_sync()
    database = request.database

    # 尝试连接到数据库，不自动创建
    db_connect_start = time.time()
    try:
        print(f"[连接] 尝试连接到数据库: {database if database else '默认数据库'}")
        db = await get_vector_db(cfg, database, create_if_not_exists=False)
        db_connect_time = time.time() - db_connect_start
        print(f"[成功] 成功连接到数据库: {database if database else '默认数据库'}, 耗时: {db_connect_time:.3f}秒")
        print(f"[时间] 数据库连接阶段: {db_connect_time:.3f}秒")
    except Exception as db_error:
        print(f"[错误] 连接数据库失败: {db_error}")
        if "database not found" in str(db_error).lower():
            return {
                "query": text,
                "results": [],
                "total_results": 0,
                "search_time": "0.000秒",
                "error": f"数据库 '{database}' 不存在"
            }
        raise HTTPException(500, f"连接数据库失败: {str(db_error)}")

    # 如果指定了数据库，打印信息
    if database:
        print(f"使用数据库: {database}")
    else:
        print("使用默认数据库")

    # 执行搜索
    print(f"执行向量搜索...")
    search_start = time.time()
    print(f"[时间] 向量搜索开始时间: {time.strftime('%H:%M:%S.%f')[:-3]}")

    # 执行向量搜索
    # 如果集合是'all'，则搜索所有集合
    if collection.lower() == 'all':
        print(f"搜索所有集合...")
    else:
        print(f"在集合 '{collection}' 中搜索...")

    # 执行搜索
    try:
        print(f"[搜索] 开始在集合 '{collection}' 中搜索...")
        print(f"[搜索] 向量维度: {len(vector)}")
        print(f"[搜索] top_k值: {top_k}")
        print(f"[时间] 搜索准备阶段完成: {time.time() - search_start:.3f}秒")

        # 处理筛选表达式
        filter_expr = ""
        filter_conditions = []

        # 1. 如果请求中直接指定了filter_expr，则使用它
        if hasattr(request, 'filter_expr') and request.filter_expr:
            filter_conditions.append(request.filter_expr)
            print(f"[搜索] 使用请求中指定的筛选表达式: {request.filter_expr}")

        # 2. 如果请求中指定了metadata_filters，则构建metadata筛选表达式
        if hasattr(request, 'metadata_filters') and request.metadata_filters:
            metadata_filter_expr = build_metadata_filter_expr(request.metadata_filters)
            if metadata_filter_expr:
                filter_conditions.append(metadata_filter_expr)
                print(f"[搜索] 构建的metadata筛选表达式: {metadata_filter_expr}")

        # 3. 检查是否有额外字段可以用于筛选（非metadata字段的直接字段筛选）
        if not filter_conditions:  # 只有在没有明确指定筛选条件时才使用额外字段
            # 获取所有标准字段名称
            standard_fields = ["text", "top_k", "collection", "database", "embedding_type", "filter_expr", "metadata_filters"]

            # 获取所有字段，包括额外字段
            try:
                # 尝试使用新的 model_dump 方法（Pydantic v2）
                all_fields = request.model_dump(exclude_unset=True)
            except AttributeError:
                # 如果失败，回退到旧的 dict 方法（Pydantic v1）
                all_fields = request.dict(exclude_unset=True)

            # 遍历所有字段
            for field, value in all_fields.items():
                # 如果不是标准字段且值不为None，则添加到筛选条件中
                if field not in standard_fields and value is not None:
                    # 根据值的类型构建筛选条件
                    if isinstance(value, str):
                        # 字符串类型，使用等于比较
                        filter_conditions.append(f"{field} == '{value}'")
                    elif isinstance(value, (int, float, bool)):
                        # 数值或布尔类型，直接使用等于比较
                        filter_conditions.append(f"{field} == {value}")

        # 合并所有筛选条件
        if filter_conditions:
            filter_expr = " and ".join(filter_conditions)
            print(f"[搜索] 最终筛选表达式: {filter_expr}")

        # 执行搜索，传入筛选表达式
        vector_search_start = time.time()
        print(f"[时间] 开始执行向量搜索: {time.strftime('%H:%M:%S.%f')[:-3]}")
        results = await db.search_vectors(collection, vector, top_k, filter_expr)
        vector_search_time = time.time() - vector_search_start
        print(f"[搜索] 搜索完成，返回结果数: {len(results)}, 耗时: {vector_search_time:.3f}秒")
        print(f"[时间] 向量搜索执行阶段: {vector_search_time:.3f}秒")

        # 打印结果摘要
        if results:
            print(f"[搜索] 结果摘要:")
            for i, r in enumerate(results[:3]):
                print(f"  - 结果 {i+1}: 内容: '{r.get('content', '')[:50]}...'")
                print(f"    距离: {r.get('distance', 0)}, 集合: {r.get('collection', collection)}")
            if len(results) > 3:
                print(f"  - ... 还有 {len(results) - 3} 条结果")
    except Exception as search_error:
        print(f"[错误] 搜索过程中出错: {search_error}")
        print(f"[错误] 异常类型: {type(search_error)}")
        print(f"[错误] 异常堆栈: {traceback.format_exc()}")

        # 如果是集合不存在的错误，返回空结果而不是错误
        if "collection not found" in str(search_error).lower() or "collection does not exist" in str(search_error).lower():
            search_time = time.time() - search_start
            total_time = time.time() - start_time
            print(f"[信息] 集合 '{collection}' 不存在，返回空结果")
            return {
                "query": text,
                "results": [],
                "total_results": 0,
                "search_time": f"{total_time:.3f}秒",
                "error": f"集合 '{collection}' 不存在"
            }

        raise HTTPException(500, f"搜索过程中出错: {str(search_error)}")

    # 如果没有结果且不是搜索所有集合，可以尝试搜索所有集合
    if not results and collection.lower() != 'all':
        print(f"[搜索] 在集合 '{collection}' 中未找到结果，尝试搜索所有集合...")
        try:
            results = await db.search_vectors('all', vector, top_k)
            print(f"[搜索] 在所有集合中搜索完成，返回结果数: {len(results)}")
        except Exception as all_search_error:
            print(f"[错误] 搜索所有集合时出错: {all_search_error}")
            print(f"[错误] 异常堆栈: {traceback.format_exc()}")

    search_time = time.time() - search_start
    print(f"[时间] 向量搜索总阶段(包括准备): {search_time:.3f}秒")

    # 打印结果信息
    total_time = time.time() - start_time
    print(f"搜索完成! 找到 {len(results)} 条结果")
    print(f"向量生成耗时: {vector_time:.3f}秒, 搜索耗时: {search_time:.3f}秒, 总耗时: {total_time:.3f}秒")

    # 格式化返回结果
    format_start = time.time()
    print(f"[时间] 开始格式化结果: {time.strftime('%H:%M:%S.%f')[:-3]}")
    formatted_results = []
    print(f"[处理] 开始格式化搜索结果...")
    try:
        for i, r in enumerate(results):
            try:
                # 获取内容
                content = r.get('content', '')
                # 计算相似度（使用余弦相似度）
                distance = r.get('distance', 0)
                # 当使用余弦相似度时，返回的距离已经是相似度值，范围在 0-1 之间
                # 这里直接使用这个值作为相似度
                similarity = distance
                # 获取集合名称
                result_collection = r.get('collection', collection)

                # 获取元数据
                metadata = r.get('metadata', {})

                # 如果内容已加密，尝试解密
                is_encrypted = metadata.get('encrypted', False) if isinstance(metadata, dict) else False
                if is_encrypted:
                    try:
                        print(f"[处理] 结果 {i+1} 内容已加密，尝试解密")
                        content = decrypt_text(content)
                        print(f"[处理] 结果 {i+1} 解密成功")
                    except Exception as decrypt_error:
                        print(f"[错误] 解密结果 {i+1} 时出错: {decrypt_error}")
                        print(f"[错误] 返回加密内容，并添加解密失败标记")
                        # 在元数据中添加解密失败标记
                        if isinstance(metadata, dict):
                            metadata['decryption_failed'] = True

                # 创建基本结果
                result = {
                    "content": content,
                    "similarity": similarity,
                    "collection": result_collection
                }

                # 如果有元数据，也包含在结果中
                if 'metadata' in r:
                    metadata = r['metadata']
                    # 如果元数据是字符串，尝试解析为JSON
                    if isinstance(metadata, str):
                        try:
                            metadata = json.loads(metadata)
                            print(f"[处理] 结果 {i+1} 元数据解析成功: {metadata}")
                        except json.JSONDecodeError as json_err:
                            print(f"[处理] 结果 {i+1} 元数据解析失败: {json_err}")
                    result['metadata'] = metadata

                # 添加所有额外字段到结果中
                for key, value in r.items():
                    # 跳过已处理的标准字段和vector字段
                    if key in ['content', 'collection', 'metadata', 'distance', 'vector']:
                        continue

                    # 将额外字段添加到结果中
                    result[key] = value

                formatted_results.append(result)
            except Exception as result_error:
                print(f"[错误] 处理结果 {i+1} 时出错: {result_error}")
                print(f"[错误] 原始结果: {r}")
    except Exception as format_error:
        print(f"[错误] 格式化结果时出错: {format_error}")
        print(f"[错误] 异常堆栈: {traceback.format_exc()}")

    # 计算格式化结果的时间
    format_time = time.time() - format_start
    print(f"[时间] 结果格式化完成: {format_time:.3f}秒")

    # 最终总时间
    final_time = time.time() - start_time
    print(f"[时间] 总耗时明细:")
    print(f"  - 向量生成: {vector_time:.3f}秒 ({vector_time/final_time*100:.1f}%)")
    print(f"  - 数据库连接: {db_connect_time:.3f}秒 ({db_connect_time/final_time*100:.1f}%)")
    print(f"  - 向量搜索: {search_time:.3f}秒 ({search_time/final_time*100:.1f}%)")
    print(f"  - 结果格式化: {format_time:.3f}秒 ({format_time/final_time*100:.1f}%)")
    print(f"  - 总计: {final_time:.3f}秒 (100%)")

    return {
        "query": text,
        "results": formatted_results,
        "total_results": len(formatted_results),
        "search_time": f"{final_time:.3f}秒",
        "time_details": {
            "vector_generation": f"{vector_time:.3f}秒",
            "db_connection": f"{db_connect_time:.3f}秒",
            "vector_search": f"{search_time:.3f}秒",
            "result_formatting": f"{format_time:.3f}秒",
            "total": f"{final_time:.3f}秒"
        }
    }


@router.post("/multi_search",
    tags=["Vector Search"],
    summary="同时搜索多个向量库"
)
async def multi_vector_search(
    request: MultiDBSearchRequest,
    raw_request: Request,
    model: EmbeddingModel = Depends(get_embed_model_sync, use_cache=True)
):
    # 如果指定了embedding_type，使用指定的向量模型
    if request.embedding_type:
        print(f"[模型] 使用指定的向量模型类型: {request.embedding_type}")
        try:
            from app.dependencies import get_embed_model_sync
            model = await get_embed_model_sync(request.embedding_type)
            print(f"[模型] 成功加载指定的向量模型: {request.embedding_type}, 维度: {model.get_dimension()}")
        except Exception as e:
            print(f"[错误] 加载指定向量模型失败: {e}")
            raise HTTPException(400, f"加载指定向量模型失败: {str(e)}")
    # 记录详细的请求信息
    print(f"\n[DEBUG] 收到多数据库搜索请求: {request}")
    print(f"[DEBUG] 请求类型: {type(request)}")
    print(f"[DEBUG] 请求字段: text={request.text}, top_k={request.top_k}, databases={request.databases}, total_results={request.total_results}")

    log_request_info(request, raw_request)

    text = request.text
    top_k = request.top_k
    total_results = request.total_results

    # 验证参数
    try:
        if isinstance(top_k, str):
            top_k = int(top_k)
        if top_k <= 0:
            raise ValueError("top_k必须大于0")

        if isinstance(total_results, str):
            total_results = int(total_results)
        if total_results <= 0:
            raise ValueError("total_results必须大于0")
    except ValueError as e:
        raise HTTPException(400, f"参数错误: {str(e)}")

    print(f"\n执行多数据库向量搜索: '{text}'")
    print(f"数据库数量: {len(request.databases)}, 每个数据库Top-K: {top_k}, 总结果数: {total_results}")
    start_time = time.time()

    # 生成查询向量
    print("生成查询向量...")
    vector_start = time.time()
    vector = await model.agenerate(text)
    vector_time = time.time() - vector_start
    print(f"向量生成完成, 维度: {len(vector)}, 耗时: {vector_time:.3f}秒")

    # 获取向量数据库配置
    from app.dependencies import get_vectordb_config_sync
    cfg = get_vectordb_config_sync()

    # 处理每个数据库的搜索请求
    db_configs = []

    # 解析数据库配置
    for db_item in request.databases:
        if isinstance(db_item, str):
            # 如果是字符串，则搜索该数据库下所有集合
            db_configs.append({
                "database": db_item,
                "collections": None  # None表示搜索所有集合
            })
        else:
            # 如果是对象，则使用指定的数据库和集合
            db_configs.append({
                "database": db_item.database,
                "collections": db_item.collections
            })

    # 处理筛选表达式
    filter_expr = ""
    filter_conditions = []

    # 1. 如果请求中直接指定了filter_expr，则使用它
    if hasattr(request, 'filter_expr') and request.filter_expr:
        filter_conditions.append(request.filter_expr)
        print(f"[搜索] 使用请求中指定的筛选表达式: {request.filter_expr}")

    # 2. 如果请求中指定了metadata_filters，则构建metadata筛选表达式
    if hasattr(request, 'metadata_filters') and request.metadata_filters:
        metadata_filter_expr = build_metadata_filter_expr(request.metadata_filters)
        if metadata_filter_expr:
            filter_conditions.append(metadata_filter_expr)
            print(f"[搜索] 构建的metadata筛选表达式: {metadata_filter_expr}")

    # 3. 检查是否有额外字段可以用于筛选（非metadata字段的直接字段筛选）
    if not filter_conditions:  # 只有在没有明确指定筛选条件时才使用额外字段
        # 获取所有标准字段名称
        standard_fields = ["text", "top_k", "databases", "total_results", "embedding_type", "filter_expr", "metadata_filters"]

        # 获取所有字段，包括额外字段
        try:
            # 尝试使用新的 model_dump 方法（Pydantic v2）
            all_fields = request.model_dump(exclude_unset=True)
        except AttributeError:
            # 如果失败，回退到旧的 dict 方法（Pydantic v1）
            all_fields = request.dict(exclude_unset=True)

        # 遍历所有字段
        for field, value in all_fields.items():
            # 如果不是标准字段且值不为None，则添加到筛选条件中
            if field not in standard_fields and value is not None:
                # 根据值的类型构建筛选条件
                if isinstance(value, str):
                    # 字符串类型，使用等于比较
                    filter_conditions.append(f"{field} == '{value}'")
                elif isinstance(value, (int, float, bool)):
                    # 数值或布尔类型，直接使用等于比较
                    filter_conditions.append(f"{field} == {value}")

    # 合并所有筛选条件
    if filter_conditions:
        filter_expr = " and ".join(filter_conditions)
        print(f"[搜索] 最终筛选表达式: {filter_expr}")

    # 创建异步搜索任务
    async def search_database(db_name, collections):
        try:
            print(f"[搜索] 开始搜索数据库 '{db_name}'...")

            # 连接到数据库，不自动创建
            try:
                print(f"[连接] 尝试连接到数据库: {db_name}")
                db = await get_vector_db(cfg, db_name, create_if_not_exists=False)
                print(f"[成功] 成功连接到数据库: {db_name}")
            except Exception as db_error:
                print(f"[错误] 连接数据库 '{db_name}' 失败: {db_error}")
                if "database not found" in str(db_error).lower():
                    print(f"[信息] 数据库 '{db_name}' 不存在，跳过该数据库")
                    return []  # 返回空结果
                raise

            # 如果指定了集合列表，则搜索指定集合
            if collections:
                print(f"[搜索] 在数据库 '{db_name}' 中搜索指定集合: {collections}")
                all_results = []

                # 对每个集合进行搜索
                for collection in collections:
                    try:
                        print(f"[搜索] 在数据库 '{db_name}' 的集合 '{collection}' 中搜索...")
                        results = await db.search_vectors(collection, vector, top_k, filter_expr)
                        print(f"[搜索] 数据库 '{db_name}' 的集合 '{collection}' 搜索完成，返回结果数: {len(results)}")

                        # 添加数据库信息到结果中
                        for r in results:
                            r['database'] = db_name

                        all_results.extend(results)
                    except Exception as coll_error:
                        print(f"[错误] 搜索数据库 '{db_name}' 的集合 '{collection}' 时出错: {coll_error}")
                        print(f"[错误] 异常堆栈: {traceback.format_exc()}")

                        # 如果是集合不存在的错误，则跳过该集合
                        if "collection not found" in str(coll_error).lower() or "collection does not exist" in str(coll_error).lower():
                            print(f"[信息] 数据库 '{db_name}' 中的集合 '{collection}' 不存在，跳过该集合")
                            continue
                        # 其他错误继续抛出
                        raise

                # 按相似度排序并限制结果数量
                # 使用余弦相似度时，值越大表示越相似，所以需要降序排序
                all_results.sort(key=lambda x: x['distance'], reverse=True)
                return all_results[:top_k]
            else:
                # 搜索所有集合
                print(f"[搜索] 在数据库 '{db_name}' 中搜索所有集合")
                try:
                    results = await db.search_vectors('all', vector, top_k, filter_expr)
                    print(f"[搜索] 数据库 '{db_name}' 搜索完成，返回结果数: {len(results)}")

                    # 添加数据库信息到结果中
                    for r in results:
                        r['database'] = db_name

                    return results
                except Exception as all_coll_error:
                    print(f"[错误] 搜索数据库 '{db_name}' 的所有集合时出错: {all_coll_error}")
                    print(f"[错误] 异常堆栈: {traceback.format_exc()}")

                    # 如果是集合不存在的错误，返回空结果
                    if "no collections available" in str(all_coll_error).lower():
                        print(f"[信息] 数据库 '{db_name}' 中没有可用的集合，返回空结果")
                        return []
                    # 其他错误继续抛出
                    raise
        except Exception as db_error:
            print(f"[错误] 搜索数据库 '{db_name}' 时出错: {db_error}")
            print(f"[错误] 异常堆栈: {traceback.format_exc()}")
            return []

    # 并行执行所有数据库的搜索
    search_start = time.time()
    search_tasks = [search_database(db_config["database"], db_config["collections"]) for db_config in db_configs]
    search_results = await asyncio.gather(*search_tasks)
    search_time = time.time() - search_start

    # 合并所有搜索结果
    all_results = []
    for results in search_results:
        all_results.extend(results)

    # 按相似度排序并限制结果数量
    # 使用余弦相似度时，值越大表示越相似，所以需要降序排序
    all_results.sort(key=lambda x: x['distance'], reverse=True)
    all_results = all_results[:total_results]

    # 打印结果信息
    total_time = time.time() - start_time
    print(f"多数据库搜索完成! 找到 {len(all_results)} 条结果")
    print(f"向量生成耗时: {vector_time:.3f}秒, 搜索耗时: {search_time:.3f}秒, 总耗时: {total_time:.3f}秒")

    # 格式化返回结果
    formatted_results = []
    print(f"[处理] 开始格式化搜索结果...")
    try:
        for i, r in enumerate(all_results):
            try:
                # 获取内容
                content = r.get('content', '')
                # 计算相似度（使用余弦相似度）
                distance = r.get('distance', 0)
                # 当使用余弦相似度时，返回的距离已经是相似度值，范围在 0-1 之间
                # 这里直接使用这个值作为相似度
                similarity = distance
                # 获取集合名称和数据库名称
                result_collection = r.get('collection', '')
                result_database = r.get('database', '')

                # 获取元数据
                metadata = r.get('metadata', {})

                # 如果内容已加密，尝试解密
                is_encrypted = metadata.get('encrypted', False) if isinstance(metadata, dict) else False
                if is_encrypted:
                    try:
                        print(f"[处理] 结果 {i+1} 内容已加密，尝试解密")
                        content = decrypt_text(content)
                        print(f"[处理] 结果 {i+1} 解密成功")
                    except Exception as decrypt_error:
                        print(f"[错误] 解密结果 {i+1} 时出错: {decrypt_error}")
                        print(f"[错误] 返回加密内容，并添加解密失败标记")
                        # 在元数据中添加解密失败标记
                        if isinstance(metadata, dict):
                            metadata['decryption_failed'] = True

                # 创建基本结果
                result = {
                    "content": content,
                    "similarity": similarity,
                    "collection": result_collection,
                    "database": result_database
                }

                # 如果有元数据，也包含在结果中
                if 'metadata' in r:
                    metadata = r['metadata']
                    # 如果元数据是字符串，尝试解析为JSON
                    if isinstance(metadata, str):
                        try:
                            metadata = json.loads(metadata)
                            print(f"[处理] 结果 {i+1} 元数据解析成功: {metadata}")
                        except json.JSONDecodeError as json_err:
                            print(f"[处理] 结果 {i+1} 元数据解析失败: {json_err}")
                    result['metadata'] = metadata

                # 添加所有额外字段到结果中
                for key, value in r.items():
                    # 跳过已处理的标准字段和vector字段
                    if key in ['content', 'collection', 'metadata', 'distance', 'database', 'vector']:
                        continue

                    # 将额外字段添加到结果中
                    result[key] = value

                formatted_results.append(result)
            except Exception as result_error:
                print(f"[错误] 处理结果 {i+1} 时出错: {result_error}")
                print(f"[错误] 原始结果: {r}")
    except Exception as format_error:
        print(f"[错误] 格式化结果时出错: {format_error}")
        print(f"[错误] 异常堆栈: {traceback.format_exc()}")

    return {
        "query": text,
        "results": formatted_results,
        "total_results": len(formatted_results),
        "search_time": f"{total_time:.3f}秒",
        "databases_searched": len(db_configs)
    }